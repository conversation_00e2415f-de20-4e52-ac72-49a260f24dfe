/**
 * 智能字体加载器 - 双层CDN降级架构
 * 基于VideoLoader架构设计，实现字体的R2优先+本地降级加载机制
 * 
 * 双层架构：
 * 1. Primary: Cloudflare R2 (5秒超时)
 * 2. Secondary: 本地字体服务 (3秒超时)
 * 
 * @version 1.0.0
 * <AUTHOR> Project Team
 */

class FontController {
    constructor() {
        this.config = null;
        this.loadingOrder = ['primary', 'secondary'];
        this.currentAttempt = 0;
        this.maxRetries = 1;

        // 生成唯一实例ID，用于调试并发问题
        this.instanceId = Math.random().toString(36).substring(2, 11);

        // 字体缓存管理
        this.fontCache = new Map();
        this.loadingPromises = new Map(); // 防止重复加载

        // 加载状态管理 - 改为请求级别，避免并发冲突
        this.loadingState = {
            initialized: false,
            currentLayer: null,
            startTime: null,
            totalAttempts: 0
        };

        console.log(`🆔 FontController实例创建: ${this.instanceId}`);
    }

    /**
     * 初始化加载器，从API获取配置
     */
    async init() {
        if (this.config) return; // 避免重复初始化
        
        try {
            console.log('🔤 初始化智能字体加载器...');
            const response = await fetch('/api/config');
            const result = await response.json();
            
            if (result.success && result.data.fontDelivery) {
                this.config = result.data.fontDelivery;
                this.loadingOrder = this.config.macroConfig?.loadingOrder || this.loadingOrder;
                
                console.log(`📋 字体策略: ${this.config.macroConfig?.strategy || 'DEFAULT'}`);
                console.log(`🔄 加载顺序: ${this.loadingOrder.join(' → ')}`);
                
                this.loadingState.initialized = true;
            } else {
                throw new Error('字体配置获取失败');
            }
        } catch (error) {
            console.error('❌ 字体配置加载失败:', error);
            // 使用默认配置
            this.loadingOrder = ['secondary']; // 降级到本地字体
        }
    }

    /**
     * 智能字体加载主函数
     * @param {string} fontFamily - 字体族名称 (如: 'Dancing Script', 'Poppins')
     * @param {Object} options - 加载选项
     * @returns {Promise<boolean>} 加载是否成功
     */
    async loadFont(fontFamily, options = {}) {
        if (!this.config) await this.init();

        // 检查缓存
        const cacheKey = this.generateCacheKey(fontFamily, options);
        if (this.fontCache.has(cacheKey)) {
            console.log(`💾 [${this.instanceId}] 字体缓存命中: ${fontFamily}`);
            return true;
        }

        // 检查是否正在加载
        if (this.loadingPromises.has(cacheKey)) {
            console.log(`⏳ [${this.instanceId}] 字体正在加载中: ${fontFamily}`);
            return await this.loadingPromises.get(cacheKey);
        }

        // 创建加载Promise
        const loadingPromise = this._performFontLoad(fontFamily, options);
        this.loadingPromises.set(cacheKey, loadingPromise);

        try {
            const result = await loadingPromise;
            if (result) {
                this.fontCache.set(cacheKey, true);
            }
            return result;
        } finally {
            this.loadingPromises.delete(cacheKey);
        }
    }

    /**
     * 执行字体加载
     * @private
     */
    async _performFontLoad(fontFamily, options) {
        // 创建请求级别的状态，避免并发冲突
        const requestState = {
            startTime: Date.now(),
            totalAttempts: 0,
            requestId: Math.random().toString(36).substring(2, 11)
        };

        console.log(`🔤 [${this.instanceId}:${requestState.requestId}] 开始智能加载字体: ${fontFamily}`);
        console.log(`📋 [${this.instanceId}] 使用策略: ${this.loadingOrder.join(' → ')}`);

        // 保存原始的错误处理函数
        const originalOnError = options.onError;
        
        for (let i = 0; i < this.loadingOrder.length; i++) {
            const layerKey = this.loadingOrder[i];
            const layerConfig = this.config.layers[layerKey];

            if (!layerConfig || !layerConfig.enabled) {
                console.log(`⏭️ [${requestState.requestId}] 跳过禁用层: ${layerKey}`);
                continue;
            }

            requestState.totalAttempts++;

            try {
                console.log(`🔄 [${requestState.requestId}] 尝试第${i + 1}层: ${layerKey} (${layerConfig.type})`);

                const url = this.generateFontUrl(fontFamily, layerKey, options);
                if (!url) {
                    console.warn(`⚠️ [${requestState.requestId}] ${layerKey} URL生成失败`);
                    continue;
                }

                console.log(`🔗 [${requestState.requestId}] 尝试加载: ${url}`);

                const success = await this.loadWithTimeout(
                    url,
                    fontFamily,
                    layerConfig.timeout,
                    {
                        ...options,
                        layerInfo: { key: layerKey, type: layerConfig.type },
                        requestId: requestState.requestId
                    }
                );

                if (success) {
                    const elapsed = Date.now() - requestState.startTime;
                    console.log(`✅ [${requestState.requestId}] 字体加载成功: ${layerKey} (${elapsed}ms)`);

                    // 调用成功回调
                    if (options.onSuccess) {
                        options.onSuccess(layerKey);
                    }

                    return true;
                }

            } catch (error) {
                console.warn(`❌ [${requestState.requestId}] ${layerKey} 加载失败: ${error.message}`);

                // 调用原始错误处理函数
                if (originalOnError) {
                    originalOnError(error, layerKey);
                }

                continue;
            }
        }

        // 所有层都失败了
        const elapsed = Date.now() - requestState.startTime;
        console.error(`❌ [${requestState.requestId}] 所有字体源加载失败 (${elapsed}ms, ${requestState.totalAttempts}次尝试)`);

        return false;
    }

    /**
     * 生成缓存键
     * @param {string} fontFamily - 字体族名称
     * @param {Object} options - 选项
     * @returns {string} 缓存键
     */
    generateCacheKey(fontFamily, options = {}) {
        const weight = options.weight || 400;
        const style = options.style || 'normal';
        return `${fontFamily}-${weight}-${style}`;
    }

    /**
     * 生成指定层的字体URL
     * @param {string} fontFamily - 字体族名称
     * @param {string} layerKey - 层级键名
     * @param {Object} options - 选项
     * @returns {string|null} 字体URL
     */
    generateFontUrl(fontFamily, layerKey, options = {}) {
        try {
            const fontConfig = this.config.fonts[fontFamily];
            if (!fontConfig) {
                console.warn(`未找到字体配置: ${fontFamily}`);
                return null;
            }

            const weight = options.weight || 400;
            const fileName = this.generateFileName(fontConfig, weight);

            switch (layerKey) {
                case 'primary': // Cloudflare R2
                    const r2Urls = this.config.urls?.r2;
                    return r2Urls ? `${r2Urls.baseUrl}${fileName}` : null;
                    
                case 'secondary': // 本地
                    const localUrls = this.config.urls?.local;
                    return localUrls ? `${localUrls.baseUrl}${fileName}` : null;
                    
                default:
                    console.warn(`未知的层级: ${layerKey}`);
                    return null;
            }
        } catch (error) {
            console.error(`URL生成失败 (${layerKey}):`, error);
            return null;
        }
    }

    /**
     * 生成字体文件名
     * @param {Object} fontConfig - 字体配置
     * @param {number} weight - 字重
     * @returns {string} 文件名
     */
    generateFileName(fontConfig, weight) {
        if (fontConfig.weightMapping && fontConfig.weightMapping[weight]) {
            return fontConfig.file.replace('{weight}', fontConfig.weightMapping[weight]);
        }
        return fontConfig.file;
    }

    /**
     * 带超时的字体加载
     * @param {string} url - 字体URL
     * @param {string} fontFamily - 字体族名称
     * @param {number} timeout - 超时时间(毫秒)
     * @param {Object} options - 选项
     * @returns {Promise<boolean>} 是否加载成功
     */
    loadWithTimeout(url, fontFamily, timeout, options = {}) {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error(`字体加载超时 (${timeout}ms)`));
            }, timeout);

            // 创建FontFace对象
            const fontFace = new FontFace(fontFamily, `url(${url})`, {
                weight: options.weight || 'normal',
                style: options.style || 'normal'
            });

            fontFace.load()
                .then(() => {
                    clearTimeout(timer);
                    
                    // 添加到document.fonts
                    document.fonts.add(fontFace);
                    
                    console.log(`🔗 字体加载成功: ${url}`);
                    resolve(true);
                })
                .catch((error) => {
                    clearTimeout(timer);
                    reject(new Error(`字体加载错误: ${error.message}`));
                });
        });
    }
}

    /**
     * 批量字体加载
     * @param {Array} fontList - 字体列表 [{family: 'Poppins', weights: [400, 700]}, ...]
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>} 加载结果统计
     */
    async loadFonts(fontList, options = {}) {
        console.log(`🔤 [${this.instanceId}] 开始批量加载 ${fontList.length} 个字体族`);

        const results = {
            total: 0,
            success: 0,
            failed: 0,
            details: []
        };

        for (const fontItem of fontList) {
            const { family, weights = [400], styles = ['normal'] } = fontItem;

            for (const weight of weights) {
                for (const style of styles) {
                    results.total++;

                    try {
                        const success = await this.loadFont(family, {
                            weight,
                            style,
                            ...options
                        });

                        if (success) {
                            results.success++;
                            results.details.push({ family, weight, style, status: 'success' });
                        } else {
                            results.failed++;
                            results.details.push({ family, weight, style, status: 'failed' });
                        }
                    } catch (error) {
                        results.failed++;
                        results.details.push({
                            family,
                            weight,
                            style,
                            status: 'error',
                            error: error.message
                        });
                    }
                }
            }
        }

        console.log(`📊 [${this.instanceId}] 批量加载完成: ${results.success}/${results.total} 成功`);
        return results;
    }

    /**
     * 预加载页面所需字体
     * @param {string} pageName - 页面名称
     * @param {Object} options - 选项
     * @returns {Promise<boolean>} 预加载是否成功
     */
    async preloadPageFonts(pageName, options = {}) {
        console.log(`🚀 [${this.instanceId}] 预加载页面字体: ${pageName}`);

        // 页面字体映射 (基于现有页面分析)
        const pageFontMapping = {
            'home': [
                { family: 'Dancing Script', weights: [400, 700] },
                { family: 'Poppins', weights: [400, 600] }
            ],
            'anniversary': [
                { family: 'Playfair Display', weights: [400, 700] },
                { family: 'Inter', weights: [400, 500] }
            ],
            'meetings': [
                { family: 'Inter', weights: [400, 500, 600] },
                { family: 'Poppins', weights: [400] }
            ],
            'memorial': [
                { family: 'Playfair Display', weights: [400, 600] },
                { family: 'Inter', weights: [400] }
            ],
            'together-days': [
                { family: 'Dancing Script', weights: [400] },
                { family: 'Poppins', weights: [400, 500] },
                { family: 'Great Vibes', weights: [400] }
            ]
        };

        const fontList = pageFontMapping[pageName] || [];
        if (fontList.length === 0) {
            console.warn(`⚠️ [${this.instanceId}] 未找到页面 ${pageName} 的字体配置`);
            return false;
        }

        const results = await this.loadFonts(fontList, options);
        return results.success > 0;
    }

    /**
     * 设置自定义加载顺序
     * @param {Array<string>} order - 加载顺序数组
     */
    setLoadingOrder(order) {
        if (Array.isArray(order) && order.length > 0) {
            this.loadingOrder = order;
            console.log(`🔄 更新字体加载顺序: ${order.join(' → ')}`);
        }
    }

    /**
     * 获取当前配置信息
     * @returns {Object} 配置信息
     */
    getConfig() {
        return {
            config: this.config,
            loadingOrder: this.loadingOrder,
            loadingState: this.loadingState,
            cacheSize: this.fontCache.size,
            supportedFonts: this.config ? Object.keys(this.config.fonts) : []
        };
    }

    /**
     * 清理字体缓存
     * @param {string} fontFamily - 可选，指定字体族
     */
    clearCache(fontFamily = null) {
        if (fontFamily) {
            // 清理特定字体的缓存
            const keysToDelete = [];
            for (const key of this.fontCache.keys()) {
                if (key.startsWith(fontFamily + '-')) {
                    keysToDelete.push(key);
                }
            }
            keysToDelete.forEach(key => this.fontCache.delete(key));
            console.log(`🗑️ [${this.instanceId}] 清理字体缓存: ${fontFamily} (${keysToDelete.length}项)`);
        } else {
            // 清理所有缓存
            const size = this.fontCache.size;
            this.fontCache.clear();
            console.log(`🗑️ [${this.instanceId}] 清理所有字体缓存 (${size}项)`);
        }
    }

    /**
     * 重置加载状态
     */
    reset() {
        this.loadingState = {
            initialized: this.loadingState.initialized,
            currentLayer: null,
            startTime: null,
            totalAttempts: 0
        };
    }

    /**
     * 页面集成辅助函数 - 自动加载页面字体
     * @param {Object} options - 集成选项
     */
    static integrateWithPage(options = {}) {
        const {
            pageName = null,
            autoDetectPage = true,
            preloadFonts = true,
            showProgress = false
        } = options;

        // 自动检测页面名称
        const detectedPageName = pageName || FontController.detectPageName();

        if (!detectedPageName) {
            console.error('❌ 无法检测页面名称，请手动指定');
            return;
        }

        console.log(`🔗 集成智能字体加载器到页面: ${detectedPageName}`);

        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                FontController._performIntegration(detectedPageName, options);
            });
        } else {
            FontController._performIntegration(detectedPageName, options);
        }
    }

    /**
     * 执行页面集成
     * @private
     */
    static _performIntegration(pageName, options) {
        const controller = window.fontController;

        if (options.preloadFonts) {
            controller.preloadPageFonts(pageName, {
                onSuccess: (layerKey) => {
                    console.log(`✅ 字体层加载成功: ${layerKey}`);
                },
                onError: (error, layerKey) => {
                    console.warn(`⚠️ 字体层加载失败 ${layerKey}: ${error.message}`);
                }
            }).then(success => {
                if (success) {
                    console.log(`🎉 ${pageName} 页面字体预加载完成`);
                } else {
                    console.error(`❌ ${pageName} 页面字体预加载失败`);
                }
            }).catch(error => {
                console.error(`❌ ${pageName} 页面字体预加载异常:`, error);
            });
        }
    }

    /**
     * 自动检测当前页面名称
     * @returns {string|null} 页面名称
     */
    static detectPageName() {
        const path = window.location.pathname;

        // 路径映射
        const pathMapping = {
            '/': 'home',
            '/index.html': 'home',
            '/together-days': 'together-days',
            '/together-days.html': 'together-days',
            '/anniversary': 'anniversary',
            '/anniversary.html': 'anniversary',
            '/meetings': 'meetings',
            '/meetings.html': 'meetings',
            '/memorial': 'memorial',
            '/memorial.html': 'memorial'
        };

        return pathMapping[path] || null;
    }

    /**
     * 宏策略快速切换
     * @param {string} strategy - 策略名称
     */
    static async switchStrategy(strategy) {
        console.log(`🔄 切换字体加载策略: ${strategy}`);

        try {
            // 重新初始化配置
            window.fontController.config = null;

            // 临时设置环境变量 (仅用于测试)
            if (window.location.search.includes('debug=true')) {
                console.log(`🧪 测试模式: 使用策略 ${strategy}`);
            }

            await window.fontController.init();
            console.log(`✅ 字体策略切换完成: ${strategy}`);

        } catch (error) {
            console.error(`❌ 字体策略切换失败:`, error);
        }
    }
}

// 创建全局实例和工具函数
window.FontController = FontController;
window.fontController = new FontController();

// 全局便捷函数
window.loadFontWithSmartLoader = (fontFamily, options = {}) => {
    return window.fontController.loadFont(fontFamily, options);
};

window.preloadPageFonts = (pageName, options = {}) => {
    FontController.integrateWithPage({ pageName, preloadFonts: true, ...options });
};

window.switchFontStrategy = FontController.switchStrategy;

console.log('📦 智能字体加载器已加载 - 支持双层CDN降级架构');
