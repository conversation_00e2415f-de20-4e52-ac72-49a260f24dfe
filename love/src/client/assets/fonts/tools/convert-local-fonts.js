#!/usr/bin/env node

/**
 * 本地字体转换工具
 * 功能：将TTF字体转换为woff2格式
 * 输入：../目录下的*.ttf文件
 * 输出：../compressed/目录下的*.woff2文件
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 本地字体配置
const LOCAL_FONTS_CONFIG = [
    {
        input: 'Courgette-Regular.ttf',
        output: 'Courgette-Regular.woff2',
        family: 'Courgette'
    },
    {
        input: 'GreatVibes-Regular.ttf',
        output: 'GreatVibes-Regular.woff2',
        family: 'Great Vibes'
    },
    {
        input: '字小魂勾玉行书(商用需授权).ttf',
        output: 'ZiXiaoHunGouYu.woff2',
        family: 'ZiXiaoHunGouYu'
    },
    {
        input: '字小魂三分行楷(商用需授权).ttf',
        output: 'ZiXiaoHunSanFen.woff2',
        family: 'ZiXiaoHunSanFen'
    },
    {
        input: '字魂行云飞白体(商用需授权).ttf',
        output: 'ZiHunXingYun.woff2',
        family: 'ZiHunXingYunFeiBai'
    }
];

// 目录配置
const INPUT_DIR = path.join(__dirname, '..');
const OUTPUT_DIR = path.join(__dirname, '../compressed');

// 确保输出目录存在
if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * 检查woff2工具是否可用
 */
function checkWoff2Tool() {
    try {
        execSync('which woff2_compress', { stdio: 'ignore' });
        return 'woff2_compress';
    } catch (error) {
        try {
            execSync('python3 -c "import fontTools.ttLib.woff2"', { stdio: 'ignore' });
            return 'fonttools';
        } catch (error2) {
            return null;
        }
    }
}

/**
 * 安装fonttools
 */
function installFonttools() {
    console.log('📦 安装fonttools工具...');
    
    try {
        execSync('pip3 install fonttools[woff]', { stdio: 'inherit' });
        console.log('✅ fonttools安装成功');
        return true;
    } catch (error) {
        console.error('❌ 自动安装失败，请手动安装:');
        console.error('  pip3 install fonttools[woff]');
        return false;
    }
}

/**
 * 转换TTF到woff2
 */
function convertToWoff2(inputPath, outputPath, tool) {
    try {
        if (tool === 'woff2_compress') {
            // 使用woff2_compress工具
            execSync(`woff2_compress "${inputPath}"`, { stdio: 'pipe' });
            // woff2_compress会在同目录生成.woff2文件
            const autoOutputPath = inputPath.replace('.ttf', '.woff2');
            if (fs.existsSync(autoOutputPath)) {
                fs.renameSync(autoOutputPath, outputPath);
            }
        } else if (tool === 'fonttools') {
            // 使用fonttools
            const command = `python3 -c "
from fontTools.ttLib import TTFont
font = TTFont('${inputPath}')
font.flavor = 'woff2'
font.save('${outputPath}')
"`;
            execSync(command, { stdio: 'pipe' });
        }
        
        return true;
    } catch (error) {
        console.error(`转换失败: ${error.message}`);
        return false;
    }
}

/**
 * 转换单个字体文件
 */
function convertFont(fontConfig) {
    const inputPath = path.join(INPUT_DIR, fontConfig.input);
    const outputPath = path.join(OUTPUT_DIR, fontConfig.output);
    
    console.log(`\n🔄 转换字体: ${fontConfig.family}`);
    console.log(`  📁 输入: ${fontConfig.input}`);
    console.log(`  📁 输出: ${fontConfig.output}`);
    
    // 检查输入文件是否存在
    if (!fs.existsSync(inputPath)) {
        console.error(`  ❌ 输入文件不存在: ${inputPath}`);
        return false;
    }
    
    // 检查输出文件是否已存在
    if (fs.existsSync(outputPath)) {
        console.log(`  ⚠️  输出文件已存在，跳过转换`);
        return true;
    }
    
    // 获取输入文件大小
    const inputStats = fs.statSync(inputPath);
    console.log(`  📊 原始大小: ${(inputStats.size / 1024 / 1024).toFixed(1)}MB`);
    
    // 检查转换工具
    let tool = checkWoff2Tool();
    if (!tool) {
        if (!installFonttools()) {
            return false;
        }
        tool = 'fonttools';
    }
    
    // 执行转换
    const success = convertToWoff2(inputPath, outputPath, tool);
    
    if (success && fs.existsSync(outputPath)) {
        const outputStats = fs.statSync(outputPath);
        const compressionRatio = ((1 - outputStats.size / inputStats.size) * 100).toFixed(1);
        console.log(`  ✅ 转换成功`);
        console.log(`  📊 压缩后: ${(outputStats.size / 1024 / 1024).toFixed(1)}MB (压缩率: ${compressionRatio}%)`);
        return true;
    } else {
        console.error(`  ❌ 转换失败`);
        return false;
    }
}

/**
 * 主函数
 */
function main() {
    console.log('🚀 开始转换本地TTF字体文件...');
    console.log(`📁 输入目录: ${INPUT_DIR}`);
    console.log(`📁 输出目录: ${OUTPUT_DIR}`);
    
    const results = [];
    
    for (const fontConfig of LOCAL_FONTS_CONFIG) {
        const success = convertFont(fontConfig);
        results.push({
            family: fontConfig.family,
            success: success
        });
    }
    
    // 统计结果
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    
    console.log('\n📊 转换结果统计:');
    console.log(`  ✅ 成功: ${successCount}/${totalCount}`);
    console.log(`  ❌ 失败: ${totalCount - successCount}/${totalCount}`);
    
    if (successCount === totalCount) {
        console.log('\n🎉 所有本地字体转换完成!');
    } else {
        console.log('\n⚠️  部分字体转换失败，请检查错误信息');
        process.exit(1);
    }
    
    // 列出所有转换的文件
    const files = fs.readdirSync(OUTPUT_DIR).filter(file => 
        file.endsWith('.woff2') && 
        LOCAL_FONTS_CONFIG.some(config => config.output === file)
    );
    
    console.log(`\n📋 已转换的本地字体文件:`);
    files.forEach(file => {
        const config = LOCAL_FONTS_CONFIG.find(c => c.output === file);
        console.log(`  - ${file} (${config.family})`);
    });
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = {
    convertFont,
    LOCAL_FONTS_CONFIG,
    INPUT_DIR,
    OUTPUT_DIR
};
