#!/usr/bin/env node

/**
 * Google Fonts下载工具
 * 功能：下载指定的Google Fonts字体文件并转换为woff2格式
 * 输出：../compressed/目录下的woff2字体文件
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// 字体配置
const FONTS_CONFIG = [
    {
        family: 'Dancing Script',
        weights: [400, 700],
        weightMapping: { 400: 'Regular', 700: 'Bold' }
    },
    {
        family: 'Poppins',
        weights: [300, 400, 500, 600, 700],
        weightMapping: { 300: 'Light', 400: 'Regular', 500: 'Medium', 600: 'SemiBold', 700: 'Bold' }
    },
    {
        family: 'Inter',
        weights: [300, 400, 500, 600, 700],
        weightMapping: { 300: 'Light', 400: 'Regular', 500: 'Medium', 600: 'SemiBold', 700: 'Bold' }
    },
    {
        family: 'Playfair Display',
        weights: [400, 500, 600, 700],
        weightMapping: { 400: 'Regular', 500: 'Medium', 600: 'SemiBold', 700: 'Bold' }
    }
];

// 输出目录
const OUTPUT_DIR = path.join(__dirname, '../compressed');

// 确保输出目录存在
if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * 下载文件
 */
function downloadFile(url, outputPath) {
    return new Promise((resolve, reject) => {
        const file = fs.createWriteStream(outputPath);
        
        https.get(url, (response) => {
            if (response.statusCode !== 200) {
                reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                return;
            }
            
            response.pipe(file);
            
            file.on('finish', () => {
                file.close();
                resolve();
            });
            
            file.on('error', (err) => {
                fs.unlink(outputPath, () => {}); // 删除不完整的文件
                reject(err);
            });
        }).on('error', (err) => {
            reject(err);
        });
    });
}

/**
 * 获取Google Fonts CSS
 */
function getGoogleFontsCss(family, weights) {
    const familyParam = family.replace(/\s+/g, '+');
    const weightsParam = weights.join(';');
    const url = `https://fonts.googleapis.com/css2?family=${familyParam}:wght@${weightsParam}&display=swap`;
    
    return new Promise((resolve, reject) => {
        https.get(url, (response) => {
            let data = '';
            
            response.on('data', (chunk) => {
                data += chunk;
            });
            
            response.on('end', () => {
                resolve(data);
            });
        }).on('error', (err) => {
            reject(err);
        });
    });
}

/**
 * 解析CSS中的字体URL
 */
function parseFontUrls(css) {
    const urlRegex = /url\((https:\/\/fonts\.gstatic\.com\/[^)]+)\)/g;
    const urls = [];
    let match;
    
    while ((match = urlRegex.exec(css)) !== null) {
        urls.push(match[1]);
    }
    
    return urls;
}

/**
 * 生成字体文件名
 */
function generateFileName(family, weight, weightMapping) {
    const familyName = family.replace(/\s+/g, '');
    const weightName = weightMapping[weight] || weight.toString();
    return `${familyName}-${weightName}.woff2`;
}

/**
 * 下载单个字体族
 */
async function downloadFontFamily(fontConfig) {
    console.log(`\n📥 下载字体族: ${fontConfig.family}`);
    
    try {
        // 获取Google Fonts CSS
        const css = await getGoogleFontsCss(fontConfig.family, fontConfig.weights);
        const fontUrls = parseFontUrls(css);
        
        if (fontUrls.length === 0) {
            throw new Error('未找到字体文件URL');
        }
        
        // 下载每个字重的字体文件
        for (let i = 0; i < fontConfig.weights.length; i++) {
            const weight = fontConfig.weights[i];
            const fileName = generateFileName(fontConfig.family, weight, fontConfig.weightMapping);
            const outputPath = path.join(OUTPUT_DIR, fileName);
            
            if (i < fontUrls.length) {
                console.log(`  ⬇️  下载 ${weight}: ${fileName}`);
                await downloadFile(fontUrls[i], outputPath);
                
                // 验证文件大小
                const stats = fs.statSync(outputPath);
                console.log(`  ✅ 完成 ${fileName} (${(stats.size / 1024).toFixed(1)}KB)`);
            } else {
                console.log(`  ⚠️  跳过 ${weight}: 未找到对应URL`);
            }
        }
        
        console.log(`✅ ${fontConfig.family} 下载完成`);
        
    } catch (error) {
        console.error(`❌ ${fontConfig.family} 下载失败:`, error.message);
        throw error;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 开始下载Google Fonts字体文件...');
    console.log(`📁 输出目录: ${OUTPUT_DIR}`);
    
    const downloadedFonts = [];
    
    try {
        for (const fontConfig of FONTS_CONFIG) {
            await downloadFontFamily(fontConfig);
            downloadedFonts.push(fontConfig.family);
        }
        
        console.log('\n🎉 所有Google Fonts下载完成!');
        console.log('📋 已下载字体族:');
        downloadedFonts.forEach(family => {
            console.log(`  - ${family}`);
        });
        
        // 列出所有下载的文件
        const files = fs.readdirSync(OUTPUT_DIR).filter(file => file.endsWith('.woff2'));
        console.log(`\n📊 总计下载 ${files.length} 个字体文件`);
        
    } catch (error) {
        console.error('\n❌ 下载过程中出现错误:', error.message);
        process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    downloadFontFamily,
    FONTS_CONFIG,
    OUTPUT_DIR
};
