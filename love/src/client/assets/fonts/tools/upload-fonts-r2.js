#!/usr/bin/env node

/**
 * Cloudflare R2 字体上传工具
 * 
 * 功能：批量上传woff2字体文件到Cloudflare R2存储桶
 * 路径：从 fonts/compressed/ 上传到 love-website/fonts/
 * 配置：基于config/.env中的R2配置
 * 
 * 使用方法：
 * node fonts/tools/upload-fonts-r2.js
 * 
 * 环境要求：
 * - @aws-sdk/client-s3 已安装
 * - config/.env 包含完整的R2配置
 * - fonts/compressed/ 目录存在字体文件
 */

const { S3Client, PutObjectCommand, HeadObjectCommand, ListObjectsV2Command } = require('@aws-sdk/client-s3');
const fs = require('fs');
const path = require('path');
// 尝试多个可能的.env路径
const possibleEnvPaths = [
    path.join(__dirname, '../../../../config/.env'),
    path.join(process.cwd(), 'config/.env'),
    path.join(__dirname, '../../../..', 'config/.env')
];

let envLoaded = false;
for (const envPath of possibleEnvPaths) {
    if (fs.existsSync(envPath)) {
        require('dotenv').config({ path: envPath });
        console.log(`📋 加载环境配置: ${envPath}`);
        envLoaded = true;
        break;
    }
}

if (!envLoaded) {
    console.error('❌ 未找到.env配置文件');
    process.exit(1);
}

// R2客户端配置
const r2Client = new S3Client({
    region: 'auto',
    endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
    credentials: {
        accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
        secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
    },
    // 强制路径样式，R2要求
    forcePathStyle: true,
});

// 配置验证
function validateConfig() {
    const requiredVars = [
        'CLOUDFLARE_R2_ACCESS_KEY_ID',
        'CLOUDFLARE_R2_SECRET_ACCESS_KEY',
        'CLOUDFLARE_R2_ENDPOINT'
    ];

    const missing = requiredVars.filter(varName => !process.env[varName]);

    if (missing.length > 0) {
        console.error('❌ 缺少必需的环境变量:');
        missing.forEach(varName => console.error(`   - ${varName}`));
        process.exit(1);
    }

    // 获取存储桶名称 - 优先使用字体专用存储桶，否则使用通用存储桶
    const bucket = process.env.CLOUDFLARE_R2_FONT_BUCKET || process.env.CLOUDFLARE_R2_BUCKET;
    if (!bucket) {
        console.error('❌ 缺少存储桶配置: CLOUDFLARE_R2_FONT_BUCKET 或 CLOUDFLARE_R2_BUCKET');
        process.exit(1);
    }

    console.log('✅ R2配置验证通过');
    console.log(`   端点: ${process.env.CLOUDFLARE_R2_ENDPOINT}`);
    console.log(`   存储桶: ${bucket}`);
    console.log(`   公共域名: ${process.env.CLOUDFLARE_R2_DOMAIN || '未配置'}`);
}

// 获取存储桶名称
function getBucketName() {
    // 优先使用现有的视频存储桶，字体存储在不同路径下
    return process.env.CLOUDFLARE_R2_BUCKET || process.env.CLOUDFLARE_R2_FONT_BUCKET;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取字体MIME类型
function getFontMimeType(fileName) {
    const ext = path.extname(fileName).toLowerCase();
    const mimeTypes = {
        '.woff2': 'font/woff2',
        '.woff': 'font/woff',
        '.ttf': 'font/ttf',
        '.otf': 'font/otf',
        '.eot': 'application/vnd.ms-fontobject'
    };
    return mimeTypes[ext] || 'application/octet-stream';
}

// 检查R2中是否已存在文件
async function checkFileExists(objectKey) {
    try {
        const headCommand = new HeadObjectCommand({
            Bucket: getBucketName(),
            Key: objectKey
        });

        const result = await r2Client.send(headCommand);
        return {
            exists: true,
            size: result.ContentLength,
            lastModified: result.LastModified,
            etag: result.ETag
        };
    } catch (error) {
        if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
            return { exists: false };
        }
        throw error;
    }
}

// 上传单个字体文件
async function uploadFont(fileName) {
    const sourceDir = path.join(__dirname, '../compressed');
    const filePath = path.join(sourceDir, fileName);
    
    // 检查源文件是否存在
    if (!fs.existsSync(filePath)) {
        console.error(`❌ 源文件不存在: ${filePath}`);
        return false;
    }

    try {
        // 读取文件
        const fileContent = fs.readFileSync(filePath);
        const fileSize = fileContent.length;
        const mimeType = getFontMimeType(fileName);
        
        console.log(`🔤 上传 ${fileName} (${formatFileSize(fileSize)})...`);

        // 构建R2对象键
        const objectKey = `love-website/fonts/${fileName}`;

        // 检查文件是否已存在
        const existsInfo = await checkFileExists(objectKey);
        if (existsInfo.exists) {
            console.log(`   📋 文件已存在，大小: ${formatFileSize(existsInfo.size)}`);
            
            // 比较文件大小，如果相同则跳过
            if (existsInfo.size === fileSize) {
                console.log(`   ⏭️ 文件大小相同，跳过上传`);
                return true;
            } else {
                console.log(`   🔄 文件大小不同，执行覆盖上传`);
            }
        }

        // 上传命令
        const uploadCommand = new PutObjectCommand({
            Bucket: getBucketName(),
            Key: objectKey,
            Body: fileContent,
            ContentType: mimeType,
            // 字体特定的缓存控制
            CacheControl: 'public, max-age=31536000, immutable',
            // R2特定的元数据
            Metadata: {
                'original-name': fileName,
                'upload-time': new Date().toISOString(),
                'file-type': 'font',
                'source': 'love-website-font-upload-script',
                'mime-type': mimeType
            }
        });

        // 执行上传
        const startTime = Date.now();
        await r2Client.send(uploadCommand);
        const uploadTime = Date.now() - startTime;

        // 验证上传结果
        const headCommand = new HeadObjectCommand({
            Bucket: getBucketName(),
            Key: objectKey
        });

        const headResult = await r2Client.send(headCommand);
        
        console.log(`✅ ${fileName} 上传成功`);
        console.log(`   大小: ${formatFileSize(headResult.ContentLength)}`);
        console.log(`   用时: ${uploadTime}ms`);
        console.log(`   ETag: ${headResult.ETag}`);
        console.log(`   MIME: ${mimeType}`);
        
        // 显示公共访问URL（如果配置了公共域名）
        if (process.env.CLOUDFLARE_R2_DOMAIN) {
            const publicUrl = `https://${process.env.CLOUDFLARE_R2_DOMAIN}/${objectKey}`;
            console.log(`   公共URL: ${publicUrl}`);
        }
        
        return true;

    } catch (error) {
        console.error(`❌ ${fileName} 上传失败:`, error.message);
        
        // 详细错误信息
        if (error.Code) {
            console.error(`   错误代码: ${error.Code}`);
        }
        if (error.$metadata?.httpStatusCode) {
            console.error(`   HTTP状态: ${error.$metadata.httpStatusCode}`);
        }
        
        return false;
    }
}

// 获取本地字体文件列表
function getFontFileList() {
    const sourceDir = path.join(__dirname, '../compressed');
    
    if (!fs.existsSync(sourceDir)) {
        console.error(`❌ 字体目录不存在: ${sourceDir}`);
        return [];
    }

    const files = fs.readdirSync(sourceDir);
    const fontFiles = files.filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ['.woff2', '.woff', '.ttf', '.otf'].includes(ext);
    });

    return fontFiles.sort();
}

// 生成字体URL映射文件
async function generateFontUrlMapping(uploadResults) {
    const mapping = {
        metadata: {
            generatedAt: new Date().toISOString(),
            totalFonts: uploadResults.length,
            successfulUploads: uploadResults.filter(r => r.success).length,
            r2Domain: process.env.CLOUDFLARE_R2_DOMAIN,
            r2Bucket: getBucketName()
        },
        fonts: {}
    };

    // 按字体族分组
    const fontFamilies = {};
    
    uploadResults.forEach(result => {
        if (!result.success) return;
        
        const fileName = result.fileName;
        const baseName = fileName.replace(/\.(woff2|woff|ttf|otf)$/, '');
        
        // 解析字体族和字重
        let family, weight = '400';
        
        if (baseName.includes('-')) {
            const parts = baseName.split('-');
            family = parts[0];
            const weightPart = parts[parts.length - 1];
            
            // 字重映射
            const weightMapping = {
                'Light': '300',
                'Regular': '400',
                'Medium': '500',
                'SemiBold': '600',
                'Bold': '700'
            };
            
            weight = weightMapping[weightPart] || '400';
        } else {
            family = baseName;
        }

        if (!fontFamilies[family]) {
            fontFamilies[family] = {};
        }

        fontFamilies[family][weight] = {
            fileName: fileName,
            r2Url: `https://${process.env.CLOUDFLARE_R2_DOMAIN}/love-website/fonts/${fileName}`,
            localUrl: `/fonts/compressed/${fileName}`,
            fileSize: result.fileSize,
            mimeType: result.mimeType
        };
    });

    mapping.fonts = fontFamilies;

    // 保存映射文件
    const mappingPath = path.join(__dirname, '../font-url-mapping.json');
    fs.writeFileSync(mappingPath, JSON.stringify(mapping, null, 2));
    
    console.log(`📋 字体URL映射文件已生成: ${mappingPath}`);
    return mapping;
}

// 验证上传后的URL可访问性
async function verifyFontUrls(mapping) {
    console.log('\n🔍 验证字体URL可访问性...');
    
    const testResults = [];
    let successCount = 0;
    let totalCount = 0;

    for (const [family, weights] of Object.entries(mapping.fonts)) {
        for (const [weight, fontInfo] of Object.entries(weights)) {
            totalCount++;
            
            try {
                const response = await fetch(fontInfo.r2Url, { method: 'HEAD' });
                const success = response.ok;
                
                testResults.push({
                    family,
                    weight,
                    url: fontInfo.r2Url,
                    success,
                    status: response.status
                });

                if (success) {
                    successCount++;
                    console.log(`✅ ${family} ${weight}: ${response.status}`);
                } else {
                    console.log(`❌ ${family} ${weight}: ${response.status}`);
                }
            } catch (error) {
                testResults.push({
                    family,
                    weight,
                    url: fontInfo.r2Url,
                    success: false,
                    error: error.message
                });
                console.log(`❌ ${family} ${weight}: ${error.message}`);
            }
        }
    }

    console.log(`\n📊 URL验证结果: ${successCount}/${totalCount} 成功`);
    return testResults;
}

// 主上传函数
async function uploadAllFonts() {
    console.log('🚀 开始批量上传字体文件到Cloudflare R2...\n');

    // 验证配置
    validateConfig();
    console.log('');

    // 获取字体文件列表
    const fontFiles = getFontFileList();

    if (fontFiles.length === 0) {
        console.error('❌ 未找到字体文件');
        process.exit(1);
    }

    console.log(`📁 发现 ${fontFiles.length} 个字体文件:`);
    fontFiles.forEach(file => console.log(`   - ${file}`));
    console.log('');

    // 统计信息
    let successCount = 0;
    let failCount = 0;
    const startTime = Date.now();
    const uploadResults = [];

    // 逐个上传字体
    for (const fontFile of fontFiles) {
        const success = await uploadFont(fontFile);

        const result = {
            fileName: fontFile,
            success: success,
            fileSize: success ? fs.statSync(path.join(__dirname, '../compressed', fontFile)).size : 0,
            mimeType: getFontMimeType(fontFile)
        };

        uploadResults.push(result);

        if (success) {
            successCount++;
        } else {
            failCount++;
        }
        console.log(''); // 空行分隔
    }

    // 上传总结
    const totalTime = Date.now() - startTime;
    console.log('📊 上传完成统计:');
    console.log(`   成功: ${successCount}/${fontFiles.length}`);
    console.log(`   失败: ${failCount}/${fontFiles.length}`);
    console.log(`   总用时: ${totalTime}ms`);

    if (successCount > 0) {
        // 生成字体URL映射
        const mapping = await generateFontUrlMapping(uploadResults);

        // 验证URL可访问性
        if (process.env.CLOUDFLARE_R2_DOMAIN) {
            await verifyFontUrls(mapping);
        }

        console.log('\n🎉 字体上传完成！');
        console.log('\n📋 后续步骤:');
        console.log('   1. 验证R2公共域名访问');
        console.log('   2. 测试前端字体加载器');
        console.log('   3. 检查双层架构降级逻辑');

        if (process.env.CLOUDFLARE_R2_DOMAIN) {
            console.log('\n🔗 测试URL示例:');
            const firstFont = uploadResults.find(r => r.success);
            if (firstFont) {
                console.log(`   https://${process.env.CLOUDFLARE_R2_DOMAIN}/love-website/fonts/${firstFont.fileName}`);
            }
        }

        // 显示字体映射摘要
        console.log('\n📋 字体映射摘要:');
        const fontFamilies = Object.keys(mapping.fonts);
        fontFamilies.forEach(family => {
            const weights = Object.keys(mapping.fonts[family]);
            console.log(`   ${family}: ${weights.join(', ')}`);
        });

    } else {
        console.log('\n⚠️ 所有字体上传失败，请检查错误信息并重试');
        process.exit(1);
    }
}

// 列出R2中现有的字体文件
async function listR2Fonts() {
    console.log('📋 列出R2存储桶中的字体文件...\n');

    try {
        const listCommand = new ListObjectsV2Command({
            Bucket: getBucketName(),
            Prefix: 'love-website/fonts/',
            MaxKeys: 1000
        });

        const result = await r2Client.send(listCommand);

        if (!result.Contents || result.Contents.length === 0) {
            console.log('📭 R2存储桶中暂无字体文件');
            return;
        }

        console.log(`📁 发现 ${result.Contents.length} 个字体文件:`);

        let totalSize = 0;
        result.Contents.forEach(obj => {
            const fileName = obj.Key.replace('love-website/fonts/', '');
            const size = obj.Size;
            const lastModified = obj.LastModified.toISOString().split('T')[0];

            console.log(`   ${fileName} (${formatFileSize(size)}, ${lastModified})`);
            totalSize += size;
        });

        console.log(`\n📊 总大小: ${formatFileSize(totalSize)}`);

        if (process.env.CLOUDFLARE_R2_DOMAIN) {
            console.log(`🔗 访问基础URL: https://${process.env.CLOUDFLARE_R2_DOMAIN}/love-website/fonts/`);
        }

    } catch (error) {
        console.error('❌ 列出R2字体文件失败:', error.message);
    }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    process.exit(1);
});

process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error);
    process.exit(1);
});

// 命令行参数处理
const args = process.argv.slice(2);

// 执行相应操作
if (require.main === module) {
    if (args.includes('--list') || args.includes('-l')) {
        // 列出现有字体
        listR2Fonts().catch(error => {
            console.error('❌ 列出字体过程发生错误:', error);
            process.exit(1);
        });
    } else {
        // 执行上传
        uploadAllFonts().catch(error => {
            console.error('❌ 上传过程发生错误:', error);
            process.exit(1);
        });
    }
}

module.exports = {
    uploadAllFonts,
    uploadFont,
    listR2Fonts,
    generateFontUrlMapping,
    verifyFontUrls
};
