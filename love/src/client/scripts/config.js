/**
 * Love项目前端配置
 * 从后端API获取.env配置，实现前后端配置统一
 * v2.2.2 - 优化重复加载问题
 */

// 前端配置对象
window.LOVE_CONFIG = null;

// 配置加载状态管理
let configLoading = false;
let configLoaded = false;
let configLoadPromise = null;

// 优化的配置加载函数 - 防重复加载
async function loadConfig() {
    // 如果已经加载完成，直接返回
    if (configLoaded && window.LOVE_CONFIG) {
        console.log('✅ 配置已存在，直接返回缓存配置');
        return window.LOVE_CONFIG;
    }

    // 如果正在加载，返回同一个Promise，避免重复请求
    if (configLoading && configLoadPromise) {
        console.log('⏳ 配置正在加载中，等待完成...');
        return configLoadPromise;
    }

    // 开始新的加载过程
    configLoading = true;
    configLoadPromise = performConfigLoad();

    try {
        const result = await configLoadPromise;
        configLoaded = true;
        configLoading = false;
        return result;
    } catch (error) {
        configLoading = false;
        configLoadPromise = null;
        throw error;
    }
}

// 实际的配置加载逻辑
async function performConfigLoad() {
    try {
        console.log('🔄 开始加载前端配置...');
        const response = await fetch('/api/config');
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                // 使用后端返回的配置
                window.LOVE_CONFIG = {
                    DOMAIN: result.data.domain.base,
                    BASE_URL: result.data.domain.url,
                    API_URL: result.data.api.baseUrl,

                    // 页面路径配置
                    PAGES: {
                        HOME: '/',
                        TOGETHER_DAYS: '/together-days',
                        ANNIVERSARY: '/anniversary',
                        MEETINGS: '/meetings',
                        MEMORIAL: '/memorial'
                    }
                };

                console.log('✅ 前端配置已从后端加载:', window.LOVE_CONFIG);
                return window.LOVE_CONFIG;
            }
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    } catch (error) {
        console.error('❌ 无法获取配置，网站无法正常工作:', error.message);
        console.error('请检查后端服务是否正常运行');

        // 不提供fallback，确保配置统一性
        window.LOVE_CONFIG = null;
        throw new Error('配置加载失败，请刷新页面重试');
    }
}

// 便捷方法
window.getPageUrl = function(page) {
    if (!window.LOVE_CONFIG) {
        throw new Error('配置未加载，请先调用 loadConfig()');
    }
    return window.LOVE_CONFIG.BASE_URL + (window.LOVE_CONFIG.PAGES[page] || page);
};

window.getHomeUrl = function() {
    if (!window.LOVE_CONFIG) {
        throw new Error('配置未加载，请先调用 loadConfig()');
    }
    return window.LOVE_CONFIG.BASE_URL + '/';
};

// 触发配置加载完成事件
function triggerConfigLoadedEvent(config, error = null) {
    const event = new CustomEvent('loveConfigLoaded', {
        detail: {
            config: config,
            error: error,
            timestamp: Date.now(),
            source: 'config.js'
        }
    });
    document.dispatchEvent(event);
    console.log('📡 配置加载完成事件已触发:', config ? '成功' : '失败');
}

// 自动加载配置 - 优化版本
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM加载完成，开始自动加载配置...');

    loadConfig().then(config => {
        // 触发配置加载完成事件
        triggerConfigLoadedEvent(config);
    }).catch(error => {
        console.error('❌ 自动配置加载失败:', error);
        // 即使配置加载失败，也触发事件，让页面使用默认配置
        triggerConfigLoadedEvent(null, error);
    });
});

// 暴露全局函数供其他脚本使用
window.loadConfig = loadConfig;

// Love前端配置模块已加载 - v2.2.2 优化版