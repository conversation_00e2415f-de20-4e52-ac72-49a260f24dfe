<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体双源架构测试</title>
    <link rel="stylesheet" href="/src/client/styles/style.css">
    <style>
        body {
            padding: 20px;
            background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 25%, #f9a8d4 50%, #f472b6 75%, #ec4899 100%);
            min-height: 100vh;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .font-test {
            margin: 15px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .font-name {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .font-sample {
            font-size: 24px;
            line-height: 1.4;
        }
        
        /* 字体测试样式 */
        .courgette { font-family: 'Courgette', cursive; }
        .great-vibes { font-family: 'Great Vibes', cursive; }
        .dancing-script { font-family: 'Dancing Script', cursive; }
        .dancing-script-bold { font-family: 'Dancing Script', cursive; font-weight: 700; }
        .poppins { font-family: 'Poppins', sans-serif; }
        .poppins-bold { font-family: 'Poppins', sans-serif; font-weight: 700; }
        .inter { font-family: 'Inter', sans-serif; }
        .inter-bold { font-family: 'Inter', sans-serif; font-weight: 700; }
        .playfair { font-family: 'Playfair Display', serif; }
        .playfair-bold { font-family: 'Playfair Display', serif; font-weight: 700; }
        .zixiaohun-gouyu { font-family: 'ZiXiaoHunGouYu', cursive; }
        .zixiaohun-sanfen { font-family: 'ZiXiaoHunSanFen', sans-serif; }
        .zihun-xingyun { font-family: 'ZiHunXingYunFeiBai', cursive; }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            background: #f0f9ff;
            border-left: 4px solid #0ea5e9;
            border-radius: 4px;
        }
        
        .url-test {
            margin: 10px 0;
            font-size: 12px;
            color: #666;
        }
        
        .url-test a {
            color: #0ea5e9;
            text-decoration: none;
        }
        
        .url-test a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #333; margin-bottom: 30px;">字体双源架构测试页面</h1>
    
    <div class="test-section">
        <h2>英文字体测试</h2>
        
        <div class="font-test">
            <div class="font-name">Courgette (Regular 400)</div>
            <div class="font-sample courgette">The quick brown fox jumps over the lazy dog. 1234567890</div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Great Vibes (Regular 400)</div>
            <div class="font-sample great-vibes">The quick brown fox jumps over the lazy dog. 1234567890</div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Dancing Script (Regular 400)</div>
            <div class="font-sample dancing-script">The quick brown fox jumps over the lazy dog. 1234567890</div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Dancing Script (Bold 700)</div>
            <div class="font-sample dancing-script-bold">The quick brown fox jumps over the lazy dog. 1234567890</div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Poppins (Regular 400)</div>
            <div class="font-sample poppins">The quick brown fox jumps over the lazy dog. 1234567890</div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Poppins (Bold 700)</div>
            <div class="font-sample poppins-bold">The quick brown fox jumps over the lazy dog. 1234567890</div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Inter (Regular 400)</div>
            <div class="font-sample inter">The quick brown fox jumps over the lazy dog. 1234567890</div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Inter (Bold 700)</div>
            <div class="font-sample inter-bold">The quick brown fox jumps over the lazy dog. 1234567890</div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Playfair Display (Regular 400)</div>
            <div class="font-sample playfair">The quick brown fox jumps over the lazy dog. 1234567890</div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Playfair Display (Bold 700)</div>
            <div class="font-sample playfair-bold">The quick brown fox jumps over the lazy dog. 1234567890</div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>中文字体测试</h2>
        
        <div class="font-test">
            <div class="font-name">字小魂勾玉行书 (ZiXiaoHunGouYu)</div>
            <div class="font-sample zixiaohun-gouyu">我们的爱情故事，从相遇到相知，每一天都是美好的回忆。1234567890</div>
        </div>
        
        <div class="font-test">
            <div class="font-name">字小魂三分行楷 (ZiXiaoHunSanFen)</div>
            <div class="font-sample zixiaohun-sanfen">我们的爱情故事，从相遇到相知，每一天都是美好的回忆。1234567890</div>
        </div>
        
        <div class="font-test">
            <div class="font-name">字魂行云飞白体 (ZiHunXingYunFeiBai)</div>
            <div class="font-sample zihun-xingyun">我们的爱情故事，从相遇到相知，每一天都是美好的回忆。1234567890</div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>双源架构URL测试</h2>
        <p>以下链接测试R2和本地字体文件的可访问性：</p>
        
        <div class="url-test">
            <strong>R2 URLs (第一层):</strong><br>
            <a href="https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Courgette-Regular.woff2" target="_blank">Courgette-Regular.woff2</a><br>
            <a href="https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/DancingScript-Regular.woff2" target="_blank">DancingScript-Regular.woff2</a><br>
            <a href="https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Poppins-Regular.woff2" target="_blank">Poppins-Regular.woff2</a>
        </div>
        
        <div class="url-test">
            <strong>本地 URLs (第二层):</strong><br>
            <a href="/fonts/compressed/Courgette-Regular.woff2" target="_blank">/fonts/compressed/Courgette-Regular.woff2</a><br>
            <a href="/fonts/compressed/DancingScript-Regular.woff2" target="_blank">/fonts/compressed/DancingScript-Regular.woff2</a><br>
            <a href="/fonts/compressed/Poppins-Regular.woff2" target="_blank">/fonts/compressed/Poppins-Regular.woff2</a>
        </div>
    </div>
    
    <div class="status">
        <h3>测试说明</h3>
        <p>1. 如果字体显示正常，说明双源架构工作正常</p>
        <p>2. 可以通过开发者工具的Network面板查看字体加载情况</p>
        <p>3. R2 URLs应该优先加载，本地URLs作为降级备用</p>
        <p>4. 点击上方链接可以直接测试字体文件的可访问性</p>
    </div>
    
    <script>
        // 检测字体加载状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 字体双源架构测试页面加载完成');
            console.log('📊 请检查Network面板查看字体加载情况');
            
            // 检测字体是否加载成功
            const testFonts = [
                'Courgette',
                'Great Vibes', 
                'Dancing Script',
                'Poppins',
                'Inter',
                'Playfair Display',
                'ZiXiaoHunGouYu',
                'ZiXiaoHunSanFen',
                'ZiHunXingYunFeiBai'
            ];
            
            testFonts.forEach(font => {
                if (document.fonts.check(`16px "${font}"`)) {
                    console.log(`✅ ${font} 字体加载成功`);
                } else {
                    console.log(`❌ ${font} 字体加载失败`);
                }
            });
        });
    </script>
</body>
</html>
