# 高画质四层备用架构部署指南 (方案A+) - AI助手实施版

## 📋 项目概述

本文档为AI助手提供Love网站高画质视频优化的完整实施方案。采用**四层备用架构**设计，结合方案A (极致画质) 和多层高可用性保障，实现99.9%可用性的桌面端视频体验。

## 🏗️ 四层备用架构设计

### 🚪 架构概览 - "四道门"策略

**设计理念**: 基于性能优化的多重保障，R2主导的高速访问体验，星空背景终极保障。

```
用户请求 → 前端智能加载器 → 尝试加载 Cloudflare R2 URL (5-8秒超时)
  |
  └─ (成功) → 播放视频 ✅ (流程结束)
  |
  └─ (失败/超时) → 尝试加载 Cloudinary URL (5-8秒超时)
      |
      └─ (成功) → 播放视频 ✅ (流程结束)
      |
      └─ (失败/超时) → 尝试加载 VPS URL (10秒超时)
          |
          └─ (成功) → 播放视频 ✅ (流程结束)
          |
          └─ (失败) → 显示星空背景 ✨ (终极保障)
```

### 🎯 四层方案详解

#### 🥇 第一层: Cloudflare R2 (主方案)
- **视频质量**: H.265压缩优化版本，最佳画质
- **超时**: 6秒
- **配额**: 10GB免费存储
- **视频源**: `src/client/assets/video-compressed/`

#### 🥈 第二层: Cloudinary (备用方案)
- **视频质量**: H.265压缩优化版本，符合100MB限制
- **超时**: 7秒
- **配额**: 150GB/月 (6个账户 × 25GB)
- **视频源**: `src/client/assets/video-compressed/`

#### 🥉 第三层: 海外VPS (底层保障)
- **视频质量**: H.265压缩优化版本，本地服务
- **超时**: 10秒
- **配额**: 服务器存储空间
- **视频源**: `src/client/assets/video-compressed/`

#### ✨ 第四层: 星空背景 (终极保障)
- **视频质量**: 统一星空背景动画
- **超时**: 无超时
- **配额**: 无限制
- **用途**: 极端情况下的用户体验保障

## 🏗️ 完整架构流程图

### 📊 从压缩到HTML调用的完整流程

```
1. 视频压缩阶段
   ┌─────────────────────────────────────────────────────────────┐
   │ src/client/assets/videos/                                   │
   │ ├── home.mp4 (63MB) ────────────────────┐                   │
   │ ├── anniversary.mp4 (102MB) ──┐         │                   │
   │ ├── meetings.mp4 (39MB) ──────┼─────────┼──────────┐        │
   │ ├── memorial.mp4 (93MB) ──────┼─┐       │          │        │
   │ └── together-days.mp4 (146MB) ┼─┼───────┼──────────┼───┐    │
   └────────────────────────────────┼─┼───────┼──────────┼───┼───┘
                                    │ │       │          │   │
                    ┌───────────────┘ │       │          │   │
                    │ ┌───────────────┘       │          │   │
                    │ │ ┌─────────────────────┘          │   │
                    │ │ │ ┌──────────────────────────────┘   │
                    │ │ │ │ ┌────────────────────────────────┘
                    ▼ ▼ ▼ ▼ ▼
   ┌─────────────────────────────────────────────────────────────┐
   │ cloudinary/compress-videos.sh (智能处理)                     │
   │ ├── 直接复制: home.mp4, meetings.mp4                         │
   │ ├── CRF 14压缩: anniversary.mp4, memorial.mp4                │
   │ └── CRF 16压缩: together-days.mp4                            │
   └─────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
   ┌─────────────────────────────────────────────────────────────┐
   │ video/compressed/ (统一输出)                                 │
   │ ├── home.mp4 (63MB - 直接复制)                               │
   │ ├── anniversary.mp4 (~60MB - H.265压缩)                      │ 
   │ ├── meetings.mp4 (39MB - 直接复制)                           │
   │ ├── memorial.mp4 (~55MB - H.265压缩)                         │
   │ └── together-days.mp4 (~90MB - H.265压缩)                    │
   └─────────────────────────────────────────────────────────────┘

2. 三层部署阶段
   video/compressed/ ──┬── cp → video/r2-upload/ ──→ Cloudflare R2
                       │
                       ├── cp → video/cloudinary-upload/ ──→ Cloudinary
                       │
                       └── 直接服务 ──→ VPS (本地项目)

3. 前端调用阶段
   ┌─────────────────────────────────────────────────────────────┐
   │ HTML页面加载                                                │
   │ ├── 获取页面名称 (home/anniversary/meetings/memorial/...)   │
   │ ├── 加载配置 (从 /api/config)                              │
   │ ├── 应用宏策略 (DEFAULT/CLOUDINARY_FIRST/VPS_FIRST...)     │
   │ └── 执行三层降级加载                                        │
   └─────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
   ┌─────────────────────────────────────────────────────────────┐
   │ 三层智能加载器 (video-loader.js)                           │
   │                                                             │
   │ 第一层: R2 (6秒超时)                                       │
   │ ├── URL: https://r2-domain/love-website/videos/page.mp4    │
   │ ├── 成功 → 播放视频 ✅                                     │
   │ └── 失败 → 第二层                                          │
   │                                                             │
   │ 第二层: Cloudinary (7秒超时)                               │
   │ ├── URL: https://res.cloudinary.com/account/video/...      │
   │ ├── 成功 → 播放视频 ✅                                     │
   │ └── 失败 → 第三层                                          │
   │                                                             │
   │ 第三层: VPS (10秒超时)                                     │
   │ ├── URL: /video/compressed/page.mp4                        │
   │ ├── 成功 → 播放视频 ✅                                     │
   │ └── 失败 → 显示静态背景 ❌                                 │
   └─────────────────────────────────────────────────────────────┘
```

## 📊 基于Love项目的详细实施方案

### 🎯 视频文件配置

#### 当前原视频文件位置 (Love项目架构)
```
love/src/client/assets/videos/
├── home/home.mp4                    # 63MB
├── anniversary/anniversary.mp4      # 102M
├── meetings/meetings.mp4            # 39MB
├── memorial/memorial.mp4            # 93MB
└── together-days/together-days.mp4  # 146MB
```

#### 视频处理架构 (H.265统一压缩版)
```
love/src/client/assets/videos/       # 原始视频管理目录
├── home/home.mp4                    # 原始视频文件 (63MB)
├── anniversary/anniversary.mp4      # 原始视频文件 (102MB)
├── meetings/meetings.mp4            # 原始视频文件 (39MB)
├── memorial/memorial.mp4            # 原始视频文件 (93MB)
├── together-days/together-days.mp4  # 原始视频文件 (146MB)
└── 洱海/DJI_0075.MP4               # 原始视频文件

love/src/client/assets/video-compressed/  # 统一压缩视频目录 (四层架构共用)
├── home.mp4                         # H.265处理 (去除音频，63MB < 90MB)
├── anniversary.mp4                  # H.265压缩至80MB (102MB → 80MB)
├── meetings.mp4                     # H.265处理 (去除音频，39MB < 90MB)
├── memorial.mp4                     # H.265压缩至80MB (93MB → 80MB)
├── together-days.mp4                # H.265压缩至80MB (146MB → 80MB)
└── erhai.mp4                        # H.265高质量压缩 (CRF 14)
```

**统一路径设计说明**：
- **单一压缩目录**: `src/client/assets/video-compressed/` - 四层架构共用
- **无重复文件夹**: R2、Cloudinary、VPS都使用同一套压缩视频
- **统一管理**: 一次压缩，多层使用，避免存储浪费
- **路径一致性**: 所有上传脚本和配置使用相同的源路径

**视频处理规则 (H.265智能策略)**：
- **小文件 (<90MB)**: home.mp4 (63MB), meetings.mp4 (39MB) - 去除音频，保持画质
- **大文件 (≥90MB)**: anniversary.mp4 (102MB), memorial.mp4 (93MB), together-days.mp4 (146MB) - H.265压缩至80MB
- **编码参数**: H.265, 2-Pass VBR, 目标文件大小80MB, 无音频, 保持原分辨率
- **统一输出**: 所有处理后的视频统一存储在 `src/client/assets/video-compressed/`

## 🔑 关键账户信息和密钥配置

### 📊 Cloudinary多账户配置 (第二道门)

| 页面 | YU编号 | 云名称 | API Key | API Secret | 视频文件 | 配额 |
|------|--------|--------|---------|------------|----------|------|
| home | YU0 | dcglebc2w | *************** | FfwmlQJX_0LOszwF6YF9KbnhmoU | home.mp4 | 25GB |
| anniversary | YU1 | drhqbbqxz | *************** | 7g-JSBacW-ccz1cSAdkHw_wCrU8 | anniversary.mp4 | 25GB |
| meetings | YU2 | dkqnm9nwr | *************** | juh_-_Amw-ds0gY03QL-E88oOIQ | meetings.mp4 | 25GB |
| memorial | YU3 | ds14sv2gh | *************** | ajE1x9E4Ynrg5AioDxJC_EZuTow | memorial.mp4 | 25GB |
| together-days | YU4 | dpq95x5nf | *************** | 849z0GBq5fapCwUCaZ0Ct0H4-5Y | together-days.mp4 | 25GB |
| (备用) | YU5 | dtsgvqrna | *************** | wgHrEwcNyzFyOceB9Q9yAHbteqc | 故障转移 | 25GB |

**总配额**: 150GB/月 (6 × 25GB)
**文件夹**: 统一使用 `love-website` 文件夹
**URL格式**: `https://res.cloudinary.com/{云名称}/video/upload/love-website/{页面名}.mp4`

### 🌐 三层架构URL映射

#### 第一道门: Cloudflare R2 (已配置)
```javascript
// R2配置信息
const r2Config = {
    accountId: "72f73ec4bceb072f1b0b044140d40a4d",
    bucket: "love-website-videos",
    endpoint: "https://72f73ec4bceb072f1b0b044140d40a4d.r2.cloudflarestorage.com",
    // 公共访问URL需要在R2控制台中启用自定义域名或使用R2.dev域名，已经在 CORS 策略中添加网站主域名 love.yuh.cool;
    publicDomain: "pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev" // 已配置完成
};

// URL模板 (公共域名配置后)
// https://your-r2-domain/love-website/videos/{page}.mp4
```

#### 第二道门: Cloudinary (多账户)
```javascript
// Cloudinary账户映射
const cloudinaryAccounts = {
    'home': 'dcglebc2w',
    'anniversary': 'drhqbbqxz',
    'meetings': 'dkqnm9nwr',
    'memorial': 'ds14sv2gh',
    'together-days': 'dpq95x5nf'
};

// URL生成函数
function getCloudinaryUrl(pageName) {
    const cloudName = cloudinaryAccounts[pageName];
    return `https://res.cloudinary.com/${cloudName}/video/upload/love-website/${pageName}.mp4`;
}
```

#### 第三道门: VPS (本地项目)
```javascript
// VPS本地路径 (直接服务)
const vpsUrls = {
    'home': '/video/compressed/home.mp4',
    'anniversary': '/video/compressed/anniversary.mp4',
    'meetings': '/video/compressed/meetings.mp4',
    'memorial': '/video/compressed/memorial.mp4',
    'together-days': '/video/compressed/together-days.mp4'
};
```

### 🔐 宏配置开关系统

```javascript
// 宏配置策略定义
const VIDEO_STRATEGIES = {
    // 生产环境 - 默认策略
    PRODUCTION: {
        order: ['primary', 'secondary', 'tertiary'], // R2 → Cloudinary → VPS
        timeouts: { primary: 6000, secondary: 7000, tertiary: 10000 },
        retries: { primary: 1, secondary: 1, tertiary: 2 }
    },

    // 开发环境 - VPS优先
    DEVELOPMENT: {
        order: ['tertiary', 'primary', 'secondary'], // VPS → R2 → Cloudinary
        timeouts: { primary: 3000, secondary: 3000, tertiary: 5000 },
        retries: { primary: 0, secondary: 0, tertiary: 1 }
    },

    // 测试环境 - Cloudinary优先
    TESTING: {
        order: ['secondary', 'primary', 'tertiary'], // Cloudinary → R2 → VPS
        timeouts: { primary: 4000, secondary: 5000, tertiary: 8000 },
        retries: { primary: 1, secondary: 1, tertiary: 1 }
    },

    // 应急模式 - 仅VPS
    EMERGENCY: {
        order: ['tertiary'], // 仅VPS
        timeouts: { tertiary: 15000 },
        retries: { tertiary: 3 }
    }
};

// 环境变量控制
const ACTIVE_STRATEGY = process.env.VIDEO_LOADING_STRATEGY || 'PRODUCTION';
```

### 🔧 配置文件设置

#### 1. 环境变量配置 (`config/.env`)
```bash
# 三层视频架构配置
VIDEO_DELIVERY_ENABLED=true
VIDEO_LOADING_STRATEGY=DEFAULT_LOADING_ORDER

# R2配置 (第一道门) - 已配置完成
CLOUDFLARE_R2_ENABLED=true
CLOUDFLARE_ACCOUNT_ID="72f73ec4bceb072f1b0b044140d40a4d"
CLOUDFLARE_R2_ACCESS_KEY_ID="9ef4ae8cec42edae5d84bd02e3b09fb0"
CLOUDFLARE_R2_SECRET_ACCESS_KEY="46d9864c2a9a3321cb5ebc82992ae121fee336333acc997faa765b610fb0e3c3"
CLOUDFLARE_R2_ENDPOINT="https://72f73ec4bceb072f1b0b044140d40a4d.r2.cloudflarestorage.com"
CLOUDFLARE_R2_BUCKET="love-website-videos"
R2_TIMEOUT=6000

# Cloudinary配置 (第二道门)
CLOUDINARY_ENABLED=true
CLOUDINARY_TIMEOUT=7000
CLOUDINARY_SECRET_YU0="FfwmlQJX_0LOszwF6YF9KbnhmoU"
CLOUDINARY_SECRET_YU1="7g-JSBacW-ccz1cSAdkHw_wCrU8"
CLOUDINARY_SECRET_YU2="juh_-_Amw-ds0gY03QL-E88oOIQ"
CLOUDINARY_SECRET_YU3="ajE1x9E4Ynrg5AioDxJC_EZuTow"
CLOUDINARY_SECRET_YU4="849z0GBq5fapCwUCaZ0Ct0H4-5Y"
CLOUDINARY_SECRET_YU5="wgHrEwcNyzFyOceB9Q9yAHbteqc"

# VPS配置 (第三道门)
VPS_ENABLED=true
VPS_BASE_URL="https://your-vps-domain.com"
VPS_TIMEOUT=10000
```

#### 2. Love项目config.js扩展
基于现有的cloudinary配置结构，在 `config/config.js` 中扩展三层架构配置：

```javascript
// 在现有cloudinary配置后添加三层视频架构配置
videoDelivery: {
    enabled: process.env.VIDEO_DELIVERY_ENABLED === 'true',

    layers: {
        primary: {
            type: 'cloudflare_r2',
            enabled: process.env.CLOUDFLARE_R2_ENABLED === 'true',
            timeout: parseInt(process.env.R2_TIMEOUT) || 6000,
            videoQuality: 'original'
        },
        secondary: {
            type: 'cloudinary',
            enabled: process.env.CLOUDINARY_ENABLED === 'true',
            timeout: parseInt(process.env.CLOUDINARY_TIMEOUT) || 7000,
            videoQuality: 'compressed',
            // 复用现有cloudinary配置
            accounts: config.cloudinary.accounts,
            pageMapping: config.cloudinary.pageMapping
        },
        tertiary: {
            type: 'vps',
            enabled: process.env.VPS_ENABLED === 'true',
            timeout: parseInt(process.env.VPS_TIMEOUT) || 10000,
            videoQuality: 'local_compressed'
        }
    },

    // H.265 2-Pass VBR压缩参数 (更新配置)
    compression: {
        codec: 'libx265',
        method: '2pass_vbr',
        targetSize: '80MB', // 大文件压缩目标
        threshold: '90MB',  // 压缩阈值
        preset: 'slow',
        noAudio: true,
        pixelFormat: 'yuv420p',
        // 2-Pass VBR特定参数
        passes: 2,
        bitrateCalculation: 'auto' // 根据目标文件大小自动计算码率
    },

    // 动态URL生成
    get urls() {
        const r2Domain = process.env.CLOUDFLARE_R2_DOMAIN;

        return {
            r2: r2Domain ? {
                home: `https://${r2Domain}/love-website/videos/home.mp4`,
                anniversary: `https://${r2Domain}/love-website/videos/anniversary.mp4`,
                meetings: `https://${r2Domain}/love-website/videos/meetings.mp4`,
                memorial: `https://${r2Domain}/love-website/videos/memorial.mp4`,
                'together-days': `https://${r2Domain}/love-website/videos/together-days.mp4`
            } : null,
            vps: {
                home: `/src/client/assets/video-compressed/home.mp4`,
                anniversary: `/src/client/assets/video-compressed/anniversary.mp4`,
                meetings: `/src/client/assets/video-compressed/meetings.mp4`,
                memorial: `/src/client/assets/video-compressed/memorial.mp4`,
                'together-days': `/src/client/assets/video-compressed/together-days.mp4`
            }
        };
    }
}
```

## 🚀 详细实施步骤

### � 第一步: 环境准备

#### 1.1 检查依赖
```bash
# 进入Love项目目录
cd love

# 检查必要工具
ffmpeg -version
node --version

# 安装依赖包
npm install cloudinary @aws-sdk/client-s3 dotenv
```

#### 1.2 创建目录结构
```bash
# 创建视频处理目录
mkdir -p video/{compressed,r2-upload,cloudinary-upload}

# 创建工具目录
mkdir -p cloudinary
```

#### 1.3 配置环境变量
在现有的 `config/.env` 文件中添加三层架构配置：
```bash
# 三层视频架构配置
VIDEO_DELIVERY_ENABLED=true
VIDEO_LOADING_STRATEGY=DEFAULT_LOADING_ORDER

# R2配置 (第一道门) - 已配置完成
CLOUDFLARE_R2_ENABLED=true
CLOUDFLARE_ACCOUNT_ID="72f73ec4bceb072f1b0b044140d40a4d"
CLOUDFLARE_R2_ACCESS_KEY_ID="9ef4ae8cec42edae5d84bd02e3b09fb0"
CLOUDFLARE_R2_SECRET_ACCESS_KEY="46d9864c2a9a3321cb5ebc82992ae121fee336333acc997faa765b610fb0e3c3"
CLOUDFLARE_R2_ENDPOINT="https://72f73ec4bceb072f1b0b044140d40a4d.r2.cloudflarestorage.com"
CLOUDFLARE_R2_BUCKET="love-website-videos"
CLOUDFLARE_R2_DOMAIN="pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev"
R2_TIMEOUT=6000

# Cloudinary配置 (第二道门) - 复用现有密钥
CLOUDINARY_TIMEOUT=7000

# VPS配置 (第三道门)
VPS_ENABLED=true
VPS_BASE_URL="https://love.yuh.cool"
VPS_TIMEOUT=10000
```

#### 1.4 配置R2公共访问域名
**重要**: 需要在Cloudflare R2控制台中配置公共访问域名

```bash
# 步骤:
1. 登录 Cloudflare Dashboard
2. 进入 R2 Object Storage → love-website-videos
3. 点击 "Settings" 标签
4. 在 "Public access" 部分点击 "Allow Access"
5. 选择 "Connect Domain" 或使用 R2.dev 域名
6. 获取公共访问域名 (格式: pub-xxxxxxx.r2.dev)
7. 将域名添加到环境变量: CLOUDFLARE_R2_DOMAIN
```

#### 1.5 扩展config.js
在现有的 `config/config.js` 中添加videoDelivery配置（已添加）

### � Cloudinary账户配置映射 (AI助手参考)

**账户信息汇总**:

| YU编号 | 云名称 | API Key | 页面映射 | 视频文件 | 配额 |
|--------|--------|---------|----------|----------|------|
| YU0 | dcglebc2w | *************** | home | home.mp4 | 25GB |
| YU1 | drhqbbqxz | *************** | anniversary | anniversary.mp4 | 25GB |
| YU2 | dkqnm9nwr | *************** | meetings | meetings.mp4 | 25GB |
| YU3 | ds14sv2gh | *************** | memorial | memorial.mp4 | 25GB |
| YU4 | dpq95x5nf | *************** | together-days | together-days.mp4 | 25GB |
| YU5 | dtsgvqrna | *************** | (备用) | 故障转移 | 25GB |

**配置要点**:
- **总配额**: 150GB/月 (6 × 25GB)
- **文件夹**: 统一使用 `love-website` 文件夹
- **视频质量**: 压缩版本，符合100MB限制
- **API密钥**: 通过环境变量安全存储
- **页面映射**: 通过算法动态生成，无需硬编码

### ⚠️ **重要提醒: 部署前置条件**

在开始视频处理之前，**必须先完成以下检查**：

#### 1. R2存储桶状态检查
```bash
当前状态: love-website-videos (空存储桶) ✅
需要操作: 上传脚本会自动创建目录结构
目标结构:
love-website/
└── videos/
    ├── home.mp4
    ├── anniversary.mp4
    ├── meetings.mp4
    ├── memorial.mp4
    └── together-days.mp4
```

#### 2. Cloudinary账户清理 (重要)
```bash
⚠️ 检测到: Cloudinary账户之前有上传过内容
必须操作: 先清空所有账户的love-website文件夹

清理步骤:
1. 登录各个Cloudinary账户
2. 进入 Media Library
3. 删除 love-website 文件夹下的所有内容
4. 确保账户配额重置

账户清理列表:
- YU0 (dcglebc2w): 需要清空
- YU1 (drhqbbqxz): 需要清空
- YU2 (dkqnm9nwr): 需要清空
- YU3 (ds14sv2gh): 需要清空
- YU4 (dpq95x5nf): 需要清空
- YU5 (dtsgvqrna): 需要清空
```

#### 3. 公共域名配置验证
```bash
✅ 已完成: pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev
验证命令: curl -I https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/
```

**⚠️ 重要**: 如果跳过Cloudinary清理步骤，可能导致配额计算错误和上传冲突。

---

### 📋 第二步: 视频压缩处理

#### 2.1 创建智能视频处理脚本 (`cloudinary/compress-videos.sh`)
```bash
#!/bin/bash
# 智能视频处理脚本 - 根据文件大小决定是否压缩

SOURCE_DIR="src/client/assets/videos"
OUTPUT_DIR="video/compressed"

echo "🎬 开始智能视频处理..."

# 处理每个视频文件
for page in home anniversary meetings memorial together-days; do
    source_file="${SOURCE_DIR}/${page}/${page}.mp4"
    output_file="${OUTPUT_DIR}/${page}.mp4"

    if [ -f "$source_file" ]; then
        echo "📹 处理 ${page}.mp4..."

        # 根据页面决定处理方式
        case "$page" in
            "home"|"meetings")
                # 小文件直接复制
                echo "📋 直接复制 ${page}.mp4 (文件较小)"
                cp "$source_file" "$output_file"
                ;;
            "together-days")
                # CRF 16压缩
                echo "🔧 H.265压缩 ${page}.mp4 (CRF: 16)"
                ffmpeg -i "$source_file" \
                    -c:v libx265 -crf 16 -preset slow \
                    -an -movflags +faststart -pix_fmt yuv420p \
                    -y "$output_file"
                ;;
            *)
                # CRF 14压缩
                echo "🔧 H.265压缩 ${page}.mp4 (CRF: 14)"
                ffmpeg -i "$source_file" \
                    -c:v libx265 -crf 14 -preset slow \
                    -an -movflags +faststart -pix_fmt yuv420p \
                    -y "$output_file"
                ;;
        esac

        echo "✅ ${page}.mp4 处理完成"
    else
        echo "❌ 源文件不存在: $source_file"
    fi
done

echo "🎉 所有视频处理完成！"
```

#### 2.2 执行处理
```bash
chmod +x cloudinary/compress-videos.sh
./cloudinary/compress-videos.sh
```

#### 2.3 验证处理结果
```bash
# 查看处理后的视频文件
ls -lh src/client/assets/videos/compressed/
```

### 📋 第三步: Cloudinary清理 (必需)

#### 3.0 清理现有Cloudinary内容
创建 `cloudinary/cleanup-cloudinary.js`:
```javascript
const cloudinary = require('cloudinary').v2;
require('dotenv').config({ path: './config/.env' });

// 账户映射
const accounts = {
    YU0: { cloudName: 'dcglebc2w', apiKey: '***************', secret: process.env.CLOUDINARY_SECRET_YU0 },
    YU1: { cloudName: 'drhqbbqxz', apiKey: '***************', secret: process.env.CLOUDINARY_SECRET_YU1 },
    YU2: { cloudName: 'dkqnm9nwr', apiKey: '***************', secret: process.env.CLOUDINARY_SECRET_YU2 },
    YU3: { cloudName: 'ds14sv2gh', apiKey: '***************', secret: process.env.CLOUDINARY_SECRET_YU3 },
    YU4: { cloudName: 'dpq95x5nf', apiKey: '***************', secret: process.env.CLOUDINARY_SECRET_YU4 },
    YU5: { cloudName: 'dtsgvqrna', apiKey: '***************', secret: process.env.CLOUDINARY_SECRET_YU5 }
};

async function cleanupCloudinary() {
    console.log('🧹 开始清理Cloudinary账户...');

    for (const [accountKey, account] of Object.entries(accounts)) {
        console.log(`\n📋 清理账户 ${accountKey} (${account.cloudName})...`);

        // 配置当前账户
        cloudinary.config({
            cloud_name: account.cloudName,
            api_key: account.apiKey,
            api_secret: account.secret
        });

        try {
            // 删除love-website文件夹下的所有资源
            const result = await cloudinary.api.delete_resources_by_prefix('love-website/', {
                resource_type: 'video'
            });

            console.log(`✅ ${accountKey}: 删除了 ${result.deleted.length} 个视频文件`);

            // 删除文件夹
            try {
                await cloudinary.api.delete_folder('love-website');
                console.log(`✅ ${accountKey}: 删除了 love-website 文件夹`);
            } catch (folderError) {
                console.log(`ℹ️ ${accountKey}: 文件夹可能已经不存在`);
            }

        } catch (error) {
            console.error(`❌ ${accountKey}: 清理失败:`, error.message);
        }
    }

    console.log('\n🎉 Cloudinary清理完成！');
}

cleanupCloudinary();
```

#### 执行清理:
```bash
node cloudinary/cleanup-cloudinary.js
```

### 📋 第四步: 三层部署

#### 4.1 部署到Cloudflare R2 (第一道门)
创建 `cloudinary/upload-r2.js`:
```javascript
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: './config/.env' });

const r2Client = new S3Client({
    region: 'auto',
    endpoint: process.env.CLOUDFLARE_R2_ENDPOINT || 'https://72f73ec4bceb072f1b0b044140d40a4d.r2.cloudflarestorage.com',
    credentials: {
        accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
        secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
    },
});

async function uploadToR2() {
    const sourceDir = 'video/r2-upload';
    const videos = ['home', 'anniversary', 'meetings', 'memorial', 'together-days'];

    console.log('🚀 开始上传H.265压缩视频到Cloudflare R2...');

    for (const video of videos) {
        const filePath = path.join(sourceDir, `${video}.mp4`);
        const fileContent = fs.readFileSync(filePath);

        const command = new PutObjectCommand({
            Bucket: process.env.CLOUDFLARE_R2_BUCKET || 'love-website-videos',
            Key: `love-website/videos/${video}.mp4`,
            Body: fileContent,
            ContentType: 'video/mp4',
            // 设置公共读取权限
            ACL: 'public-read'
        });

        try {
            await r2Client.send(command);
            console.log(`✅ ${video}.mp4 上传到R2成功`);
        } catch (error) {
            console.error(`❌ ${video}.mp4 上传失败:`, error.message);
        }
    }

    console.log('🎉 R2上传完成！');
}

uploadToR2();
```

#### 4.2 部署到Cloudinary (第二道门)

**⚠️ 重要**: 执行上传前，必须先清空所有Cloudinary账户的love-website文件夹！

创建 `cloudinary/upload-cloudinary.js`:
```javascript
const cloudinary = require('cloudinary').v2;
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: './config/.env' });

// 账户映射
const accounts = {
    home: { cloudName: 'dcglebc2w', apiKey: '***************', secret: process.env.CLOUDINARY_SECRET_YU0 },
    anniversary: { cloudName: 'drhqbbqxz', apiKey: '***************', secret: process.env.CLOUDINARY_SECRET_YU1 },
    meetings: { cloudName: 'dkqnm9nwr', apiKey: '***************', secret: process.env.CLOUDINARY_SECRET_YU2 },
    memorial: { cloudName: 'ds14sv2gh', apiKey: '***************', secret: process.env.CLOUDINARY_SECRET_YU3 },
    'together-days': { cloudName: 'dpq95x5nf', apiKey: '***************', secret: process.env.CLOUDINARY_SECRET_YU4 }
};

async function uploadToCloudinary() {
    const sourceDir = 'video/cloudinary-upload';

    console.log('☁️ 开始上传H.265压缩视频到Cloudinary...');

    for (const [page, account] of Object.entries(accounts)) {
        // 配置当前账户
        cloudinary.config({
            cloud_name: account.cloudName,
            api_key: account.apiKey,
            api_secret: account.secret
        });

        const filePath = path.join(sourceDir, `${page}.mp4`);

        try {
            const result = await cloudinary.uploader.upload(filePath, {
                resource_type: 'video',
                public_id: `love-website/${page}`,
                overwrite: true
            });

            console.log(`✅ ${page}.mp4 上传到 ${account.cloudName} 成功`);
        } catch (error) {
            console.error(`❌ ${page}.mp4 上传失败:`, error.message);
        }
    }

    console.log('🎉 Cloudinary上传完成！');
}

uploadToCloudinary();
```

#### 4.3 VPS配置 (第三道门 - 本地项目)
由于项目本身就在VPS上，无需单独部署脚本。

**Nginx配置** (基于现有配置扩展):
```nginx
# 在现有的server块中添加视频文件服务配置
server {
    listen 443 ssl;
    server_name love.yuh.cool;

    # SSL配置 (使用现有配置)
    # ...

    # 项目根目录 (使用现有配置)
    # ...

    # 视频文件直接服务 (新增)
    location /video/compressed/ {
        alias /root/workspace/love/video/compressed/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";

        # 确保视频文件MIME类型正确
        location ~* \.(mp4|webm|ogg)$ {
            add_header Content-Type video/mp4;
        }
    }

    # 现有的静态文件和API配置保持不变
    # ...
}
```

### 📋 第五步: 智能加载器集成

#### 5.1 创建智能加载器 (`cloudinary/video-loader.js`)
```javascript
// 三层智能视频加载器 - 集成到Love项目
class VideoLoader {
    constructor() {
        this.config = null;
        this.loadingOrder = ['primary', 'secondary', 'tertiary'];
    }

    async init() {
        // 从Love项目配置API获取配置
        try {
            const response = await fetch('/api/config');
            const result = await response.json();
            this.config = result.data.videoDelivery;
            this.loadingOrder = this.config.macroConfig?.loadingOrder || this.loadingOrder;
        } catch (error) {
            console.error('配置加载失败:', error);
        }
    }

    async loadVideo(pageName, videoElement) {
        if (!this.config) await this.init();

        for (const layerKey of this.loadingOrder) {
            const layerConfig = this.config.layers[layerKey];
            if (!layerConfig.enabled) continue;

            try {
                const url = this.generateVideoUrl(pageName, layerKey);
                const success = await this.loadWithTimeout(url, videoElement, layerConfig.timeout);

                if (success) {
                    console.log(`✅ 视频加载成功: ${layerKey} - ${pageName}`);
                    return true;
                }
            } catch (error) {
                console.warn(`❌ ${layerKey} 加载失败: ${error.message}`);
                continue;
            }
        }

        throw new Error('所有视频源加载失败');
    }

    generateVideoUrl(pageName, layerKey) {
        switch (layerKey) {
            case 'primary': // R2
                return this.config.urls.r2[pageName];
            case 'secondary': // Cloudinary
                return this.generateCloudinaryUrl(pageName);
            case 'tertiary': // VPS
                return this.config.urls.vps[pageName];
            default:
                throw new Error(`未知的层级: ${layerKey}`);
        }
    }

    generateCloudinaryUrl(pageName) {
        // 使用现有config.js中的cloudinary配置
        const pageMapping = this.config.layers.secondary.pageMapping || {
            'home': 'INDEX',
            'anniversary': 'ANNIVERSARY',
            'meetings': 'MEETINGS',
            'memorial': 'MEMORIAL',
            'together-days': 'TOGETHER_DAYS'
        };

        const accountKey = pageMapping[pageName];
        const accounts = this.config.layers.secondary.accounts;

        if (accounts && accounts[accountKey]) {
            const cloudName = accounts[accountKey].cloudName;
            return `https://res.cloudinary.com/${cloudName}/video/upload/love-website/${pageName}.mp4`;
        }

        // 降级到硬编码映射
        const fallbackMapping = {
            'home': 'dcglebc2w',
            'anniversary': 'drhqbbqxz',
            'meetings': 'dkqnm9nwr',
            'memorial': 'ds14sv2gh',
            'together-days': 'dpq95x5nf'
        };

        const cloudName = fallbackMapping[pageName];
        return `https://res.cloudinary.com/${cloudName}/video/upload/love-website/${pageName}.mp4`;
    }

    loadWithTimeout(url, videoElement, timeout) {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                videoElement.onloadeddata = null;
                videoElement.onerror = null;
                reject(new Error('加载超时'));
            }, timeout);

            // 先设置事件监听器，再设置src
            videoElement.onloadeddata = () => {
                clearTimeout(timer);
                videoElement.onloadeddata = null;
                videoElement.onerror = null;
                resolve(true);
            };

            videoElement.onerror = () => {
                clearTimeout(timer);
                videoElement.onloadeddata = null;
                videoElement.onerror = null;
                reject(new Error('视频加载错误'));
            };

            // 最后设置src触发加载
            videoElement.src = url;
        });
    }

    // 设置自定义加载顺序
    setLoadingOrder(order) {
        this.loadingOrder = order;
    }

    // 获取当前配置信息
    getConfig() {
        return {
            config: this.config,
            loadingOrder: this.loadingOrder,
            supportedPages: ['home', 'anniversary', 'meetings', 'memorial', 'together-days']
        };
    }
}

// 全局实例
window.videoLoader = new VideoLoader();
```

---

#### 5.2 完整的HTML集成方案

**在HTML页面中的完整实现**：
```html
<!DOCTYPE html>
<html>
<head>
    <title>Love Website - 页面名称</title>
</head>
<body>
    <!-- 背景视频元素 -->
    <video id="background-video" autoplay muted loop playsinline>
        <source src="" type="video/mp4">
        您的浏览器不支持视频播放
    </video>

    <!-- 引入智能加载器 -->
    <script src="/cloudinary/video-loader.js"></script>

    <script>
    // 宏配置开关 - 可在编译时或运行时切换
    const VIDEO_LOADING_STRATEGY = {
        // 默认策略: R2 → Cloudinary → VPS
        DEFAULT: ['primary', 'secondary', 'tertiary'],
        // Cloudinary优先: Cloudinary → R2 → VPS
        CLOUDINARY_FIRST: ['secondary', 'primary', 'tertiary'],
        // VPS优先: VPS → R2 → Cloudinary
        VPS_FIRST: ['tertiary', 'primary', 'secondary'],
        // 仅R2: 只使用R2
        R2_ONLY: ['primary'],
        // 仅Cloudinary: 只使用Cloudinary
        CLOUDINARY_ONLY: ['secondary'],
        // 仅VPS: 只使用VPS
        VPS_ONLY: ['tertiary']
    };

    // 当前激活的策略 (可通过环境变量或配置切换)
    const ACTIVE_STRATEGY = VIDEO_LOADING_STRATEGY.DEFAULT;

    // 获取当前页面名称
    function getCurrentPageName() {
        const path = window.location.pathname;
        if (path === '/' || path === '/index.html') return 'home';
        return path.replace('/', '').replace('.html', '');
    }

    // 智能视频加载
    async function loadPageVideo() {
        const videoElement = document.querySelector('#background-video');
        const pageName = getCurrentPageName();

        console.log(`🎬 开始加载视频: ${pageName}`);
        console.log(`📋 使用策略: ${ACTIVE_STRATEGY.join(' → ')}`);

        try {
            // 设置自定义加载策略
            window.videoLoader.setLoadingOrder(ACTIVE_STRATEGY);

            await window.videoLoader.loadVideo(pageName, videoElement);
            console.log('✅ 视频加载成功');

            // 视频加载成功后的处理
            videoElement.style.opacity = '1';

        } catch (error) {
            console.error('❌ 视频加载失败:', error);

            // 降级处理: 显示静态背景
            document.body.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            videoElement.style.display = 'none';
        }
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', loadPageVideo);

    // 可选: 添加重试机制
    window.addEventListener('online', () => {
        console.log('🌐 网络恢复，重新尝试加载视频');
        loadPageVideo();
    });
    </script>
</body>
</html>
```

### 📋 第六步: 验证和测试

#### 5.1 功能验证
```bash
# 1. 验证配置加载
curl https://love.yuh.cool/api/config | jq '.data.videoDelivery'

# 2. 测试R2访问 (已配置完成)
curl -I https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/videos/home.mp4

# 方式2: 通过API验证上传
aws s3 ls s3://love-website-videos/love-website/videos/ --endpoint-url https://72f73ec4bceb072f1b0b044140d40a4d.r2.cloudflarestorage.com

# 3. 测试Cloudinary访问
curl -I https://res.cloudinary.com/dcglebc2w/video/upload/love-website/home.mp4

# 4. 测试VPS访问
curl -I https://love.yuh.cool/video/compressed/home.mp4
```

---

## 📋 完整执行清单

### ✅ 实施步骤总览

1. **环境准备**
   - [ ] 检查ffmpeg和Node.js
   - [ ] 安装依赖包
   - [ ] 创建目录结构
   - [ ] 配置环境变量
   - [ ] 扩展config.js

2. **视频处理**
   - [ ] 创建压缩脚本
   - [ ] 执行三层压缩
   - [ ] 验证输出文件

3. **Cloudinary清理** (重要)
   - [ ] 创建清理脚本
   - [ ] 执行清理所有账户
   - [ ] 验证清理完成

4. **三层部署**
   - [ ] 部署到Cloudflare R2
   - [ ] 部署到Cloudinary
   - [ ] 部署到VPS

5. **前端集成**
   - [ ] 创建智能加载器
   - [ ] 集成到Love项目
   - [ ] 测试加载机制

6. **验证测试**
   - [ ] 功能验证
   - [ ] 性能测试
   - [ ] 用户体验确认

### 🎯 关键要点

- **配置管理**: 遵循Love项目规范，统一存储在 `config/.env`
- **视频编码**: H.265编码，无音频，保持原分辨率
- **质量控制**: CRF 14 (together-days使用CRF 16)
- **统一架构**: 三层都使用同一套H.265压缩视频
- **智能降级**: R2→Cloudinary→VPS自动切换
- **用户体验**: 切换过程无感知

## � 项目文件结构

### 🗂️ 完整目录结构
```
love/
├── config/
│   ├── config.js                    # ✅ 扩展videoDelivery配置
│   └── .env                         # 🔧 添加三层架构环境变量
├── cloudinary/                      # 三层架构工具目录
│   ├── 高画质Cloudinary部署指南.md   # ✅ 本文档
│   ├── compress-videos.sh           # 🔧 H.265视频压缩脚本
│   ├── upload-r2.js                 # 🔧 R2上传工具
│   ├── upload-cloudinary.js         # 🔧 Cloudinary上传工具
│   ├── deploy-vps.js                # 🔧 VPS部署工具
│   └── video-loader.js              # 🔧 智能加载器
├── src/client/assets/videos/        # ✅ 原始视频文件
│   ├── home/home.mp4                # 63MB
│   ├── anniversary/anniversary.mp4  # 已剪辑
│   ├── meetings/meetings.mp4        # 39MB
│   ├── memorial/memorial.mp4        # 93MB
│   └── together-days/together-days.mp4 # 146MB
└── video/                           # 🔧 视频处理目录
    ├── compressed/                  # 处理后的视频 (无音频)
    │   ├── home.mp4                 # 直接复制 (63MB)
    │   ├── anniversary.mp4          # CRF 14压缩 (102MB→~60MB)
    │   ├── meetings.mp4             # 直接复制 (39MB)
    │   ├── memorial.mp4             # CRF 14压缩 (93MB→~55MB)
    │   └── together-days.mp4        # CRF 16压缩 (146MB→~90MB)
    ├── r2-upload/                   # R2上传准备
    └── cloudinary-upload/           # Cloudinary上传准备
```

### 🔧 工具脚本说明

- **compress-videos.sh**: H.265视频压缩脚本，无音频，CRF 14/16
- **cleanup-cloudinary.js**: Cloudinary清理脚本，清空所有账户的love-website文件夹
- **upload-r2.js**: Cloudflare R2上传工具，上传H.265压缩视频
- **upload-cloudinary.js**: Cloudinary多账户上传工具，自动分配账户
- **video-loader.js**: 智能加载器，实现三层降级逻辑

**注意**: VPS不需要单独的部署脚本，直接通过Nginx服务`video/compressed/`目录

### 🎯 视频规格说明

- **编码**: H.265 (libx265) - 更高压缩效率
- **音频**: 无音频 (-an) - 背景视频不需要音频
- **分辨率**: 保持原分辨率 - 不强制缩放
- **质量**: CRF 14/16 - 视觉无损质量
- **预设**: slow - 平衡质量和编码速度
- **像素格式**: yuv420p - 确保所有浏览器和设备兼容性

**像素格式说明**：
- yuv420p是最广泛支持的像素格式
- 确保视频在所有现代浏览器中正常播放
- 兼容移动设备和桌面设备
- 是HTML5视频标准推荐格式

---

**文档版本**: v6.2.0 (R2配置完成版)
**创建时间**: 2025-01-31
**更新时间**: 2025-08-01
**适用项目**: Love Website 三层备用架构优化
**维护者**: AI Assistant
**架构设计**: R2主导三层高可用性方案 + 统一配置管理

**v6.2.0 更新内容**:
- ✅ 集成真实的Cloudflare R2配置信息
- ✅ 更新API密钥和端点配置
- ✅ 添加R2公共域名配置指南
- ✅ 更新上传脚本的实际参数
- ✅ 添加配置验证和测试步骤
- ⚠️ 待完成: R2公共域名启用 (需要在控制台操作)

**v6.1.0 更新内容**:
- 修正配置结构，基于现有config.js扩展
- 修正API端点实现，扩展现有/api/config
- 修正VPS路径配置，使用实际项目路径
- 修正video-loader.js事件处理逻辑
- 添加video目录结构和README说明
- 更新环境变量配置，复用现有密钥
