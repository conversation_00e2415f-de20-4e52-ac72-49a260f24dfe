<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R2视频加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            padding: 15px 30px;
            margin: 0 10px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .btn.reset {
            background: #f44336;
        }
        .btn.single {
            background: #ff9800;
        }
        .status {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .video-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
        }
        .video-container {
            width: 100%;
            height: 200px;
            background: #000;
            border-radius: 8px;
            position: relative;
            margin-bottom: 15px;
        }
        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
        }
        .info {
            font-size: 14px;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
        .status-waiting {
            background: #ff9800;
        }
        .status-loading {
            background: #2196F3;
        }
        .status-success {
            background: #4CAF50;
        }
        .status-error {
            background: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 R2视频加载测试</h1>
            <p>测试R2存储桶视频加载功能 | 串行加载 | 并发问题解决</p>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="startSerialTest()">🚀 开始串行测试</button>
            <button class="btn reset" onclick="resetAllTests()">🔄 重置测试</button>
            <button class="btn single" onclick="testFirstVideo()">🎯 测试第一个</button>
        </div>
        
        <div class="status">
            <strong>当前状态：</strong><span id="currentStatus">等待开始测试</span>
        </div>
        
        <div class="video-grid" id="videoGrid">
            <!-- 视频项目将通过JavaScript生成 -->
        </div>
        
        <div class="status">
            <h3>📊 测试结果</h3>
            <div id="testResults">等待测试开始...</div>
        </div>
    </div>

    <!-- 引入依赖 -->
    <script src="../src/client/scripts/config.js"></script>
    <script src="../src/client/scripts/video-loader.js"></script>
    
    <script>
        // 测试数据
        const videos = [
            { name: 'home', title: '首页视频' },
            { name: 'anniversary', title: '纪念日视频' },
            { name: 'meetings', title: '相遇记录视频' },
            { name: 'memorial', title: '纪念页面视频' },
            { name: 'together-days', title: '在一起的日子视频' }
        ];
        
        let testResults = {
            total: videos.length,
            success: 0,
            failed: 0,
            times: []
        };
        
        // 更新状态
        function updateStatus(message) {
            document.getElementById('currentStatus').textContent = message;
            console.log('📊 状态:', message);
        }
        
        // 创建视频元素
        function createVideoElements() {
            const grid = document.getElementById('videoGrid');
            grid.innerHTML = '';
            
            videos.forEach(video => {
                const item = document.createElement('div');
                item.className = 'video-item';
                item.innerHTML = `
                    <div class="video-container">
                        <video id="video-${video.name}" muted loop playsinline preload="metadata">
                            <source src="/src/client/assets/video-compressed/${video.name}.mp4" type="video/mp4">
                        </video>
                        <div class="loading" id="loading-${video.name}">等待测试</div>
                    </div>
                    <div class="info">
                        <h4>${video.title}</h4>
                        <div class="status-badge status-waiting" id="status-${video.name}">等待测试</div>
                        <div id="info-${video.name}">
                            <div>文件: ${video.name}.mp4</div>
                            <div>状态: 等待中</div>
                        </div>
                    </div>
                `;
                grid.appendChild(item);
            });
        }
        
        // 重置测试
        function resetAllTests() {
            console.log('🔄 重置所有测试');
            updateStatus('测试已重置');
            testResults = { total: videos.length, success: 0, failed: 0, times: [] };
            createVideoElements();
            document.getElementById('testResults').textContent = '等待测试开始...';
        }
        
        // 测试第一个视频
        async function testFirstVideo() {
            console.log('🎯 测试第一个视频');
            updateStatus('测试第一个视频: ' + videos[0].title);
            
            try {
                await testSingleVideo(videos[0], 1);
                updateStatus('第一个视频测试完成');
            } catch (error) {
                console.error('❌ 第一个视频测试失败:', error);
                updateStatus('第一个视频测试失败: ' + error.message);
            }
        }
        
        // 开始串行测试
        async function startSerialTest() {
            console.log('🚀 开始串行测试');
            updateStatus('开始串行测试所有视频...');
            resetAllTests();
            
            for (let i = 0; i < videos.length; i++) {
                const video = videos[i];
                updateStatus(`测试 ${i + 1}/${videos.length}: ${video.title}`);
                
                try {
                    await testSingleVideo(video, i + 1);
                    console.log(`✅ 完成 ${i + 1}/${videos.length}: ${video.name}`);
                } catch (error) {
                    console.error(`❌ 失败 ${i + 1}/${videos.length}: ${video.name}`, error);
                }
                
                // 间隔
                if (i < videos.length - 1) {
                    updateStatus(`等待1秒后继续下一个...`);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
            
            updateStatus('所有测试完成');
            showResults();
        }
        
        // 测试单个视频
        async function testSingleVideo(video, index) {
            console.log(`🎬 测试视频 ${index}: ${video.name}`);
            
            const videoElement = document.getElementById(`video-${video.name}`);
            const loadingElement = document.getElementById(`loading-${video.name}`);
            const statusElement = document.getElementById(`status-${video.name}`);
            const infoElement = document.getElementById(`info-${video.name}`);
            
            // 更新状态
            statusElement.textContent = '加载中';
            statusElement.className = 'status-badge status-loading';
            loadingElement.textContent = '加载中...';
            
            const startTime = Date.now();
            
            try {
                // 创建独立的VideoLoader实例
                const loader = new VideoLoader();
                console.log(`🔄 [${index}] 创建独立加载器实例`);
                
                const success = await loader.loadVideo(video.name, videoElement, {
                    onProgress: (progress) => {
                        loadingElement.textContent = `加载中... ${Math.round(progress)}%`;
                    },
                    onError: (error, layer) => {
                        console.warn(`⚠️ [${index}] ${video.name} ${layer} 失败:`, error.message);
                    }
                });
                
                const endTime = Date.now();
                const loadTime = endTime - startTime;
                
                if (success) {
                    statusElement.textContent = '成功';
                    statusElement.className = 'status-badge status-success';
                    loadingElement.textContent = '加载完成';
                    
                    infoElement.innerHTML = `
                        <div>文件: ${video.name}.mp4</div>
                        <div>状态: ✅ 成功</div>
                        <div>时间: ${loadTime}ms</div>
                    `;
                    
                    testResults.success++;
                    testResults.times.push(loadTime);
                    
                    console.log(`✅ [${index}] ${video.name} 成功: ${loadTime}ms`);
                } else {
                    throw new Error('加载失败');
                }
                
            } catch (error) {
                const endTime = Date.now();
                const loadTime = endTime - startTime;
                
                statusElement.textContent = '失败';
                statusElement.className = 'status-badge status-error';
                loadingElement.textContent = '加载失败';
                
                infoElement.innerHTML = `
                    <div>文件: ${video.name}.mp4</div>
                    <div>状态: ❌ 失败</div>
                    <div>时间: ${loadTime}ms</div>
                    <div>错误: ${error.message}</div>
                `;
                
                testResults.failed++;
                console.error(`❌ [${index}] ${video.name} 失败:`, error);
                throw error;
            }
        }
        
        // 显示结果
        function showResults() {
            const avgTime = testResults.times.length > 0 
                ? Math.round(testResults.times.reduce((a, b) => a + b, 0) / testResults.times.length)
                : 0;
            
            const successRate = ((testResults.success / testResults.total) * 100).toFixed(1);
            
            document.getElementById('testResults').innerHTML = `
                <div>总数: ${testResults.total}</div>
                <div>成功: ${testResults.success}</div>
                <div>失败: ${testResults.failed}</div>
                <div>成功率: ${successRate}%</div>
                <div>平均时间: ${avgTime}ms</div>
            `;
            
            console.log('📊 测试结果:', testResults);
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎯 调试测试页面已加载');
            createVideoElements();
            updateStatus('页面已准备就绪，点击按钮开始测试');
        });
    </script>
</body>
</html>
