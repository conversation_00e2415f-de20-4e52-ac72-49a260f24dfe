<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPS视频加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            padding: 15px 30px;
            margin: 0 10px;
            background: #27ae60;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .btn.reset {
            background: #e74c3c;
        }
        .btn.single {
            background: #f39c12;
        }
        .status {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .video-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
        }
        .video-container {
            width: 100%;
            height: 200px;
            background: #000;
            border-radius: 8px;
            position: relative;
            margin-bottom: 15px;
        }
        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
        }
        .info {
            font-size: 14px;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
        .status-waiting {
            background: #f39c12;
            color: white;
        }
        .status-loading {
            background: #3498db;
            color: white;
        }
        .status-success {
            background: #27ae60;
            color: white;
        }
        .status-error {
            background: #e74c3c;
            color: white;
        }
        .performance-stats {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .stat-item {
            display: inline-block;
            margin: 5px 15px;
            padding: 8px 12px;
            background: rgba(255,255,255,0.2);
            border-radius: 5px;
        }
        .log-container {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖥️ VPS视频加载测试</h1>
            <p>测试第三层保障：VPS本地视频服务功能</p>
            <p><strong>策略模式</strong>: VPS_ONLY - 仅使用本地服务器</p>
            <p><strong>测试目标</strong>: 验证Nginx配置、缓存策略、CORS设置和本地文件服务性能</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="startSerialTest()">🚀 开始串行测试</button>
            <button class="btn single" onclick="testFirstVideo()">🎯 测试第一个</button>
            <button class="btn reset" onclick="resetTest()">🔄 重置测试</button>
        </div>

        <div class="status" id="globalStatus">
            <strong>状态</strong>: 等待开始测试
        </div>

        <div class="performance-stats" id="performanceStats" style="display: none;">
            <h3>📊 性能统计</h3>
            <div id="statsContent"></div>
        </div>

        <div class="video-grid" id="videoGrid">
            <!-- 视频测试项将通过JavaScript动态生成 -->
        </div>

        <div class="log-container" id="logContainer">
            <strong>📋 测试日志</strong><br>
            等待开始测试...
        </div>
    </div>

    <!-- 引入智能视频加载器 -->
    <script src="/src/client/scripts/video-loader.js"></script>

    <script>
        // 测试配置
        const TEST_CONFIG = {
            strategy: 'VPS_ONLY',
            pages: ['home', 'anniversary', 'meetings', 'memorial', 'together-days'],
            pageNames: {
                'home': '🏠 首页',
                'anniversary': '💕 纪念日',
                'meetings': '👫 相遇记录',
                'memorial': '🌟 纪念页面',
                'together-days': '💑 在一起的日子'
            },
            timeout: 10000, // VPS超时时间
            serialDelay: 1000 // 串行测试间隔
        };

        // 测试状态
        let testState = {
            isRunning: false,
            currentIndex: 0,
            results: {},
            startTime: null,
            videoLoader: null
        };

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const color = {
                'info': '#ffffff',
                'success': '#27ae60',
                'error': '#e74c3c',
                'warning': '#f39c12'
            }[type] || '#ffffff';
            
            logContainer.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[VPS测试] ${message}`);
        }

        // 初始化页面
        function initializePage() {
            log('🖥️ 初始化VPS视频加载测试页面');
            
            // 创建独立的VideoLoader实例
            testState.videoLoader = new VideoLoader();
            log(`🆔 创建VideoLoader实例: ${testState.videoLoader.instanceId}`);
            
            // 生成视频测试项
            generateVideoItems();
            
            log('✅ 页面初始化完成');
        }

        // 生成视频测试项
        function generateVideoItems() {
            const videoGrid = document.getElementById('videoGrid');
            videoGrid.innerHTML = '';

            TEST_CONFIG.pages.forEach(page => {
                const videoItem = document.createElement('div');
                videoItem.className = 'video-item';
                videoItem.id = `video-item-${page}`;
                
                videoItem.innerHTML = `
                    <h3>${TEST_CONFIG.pageNames[page]}</h3>
                    <div class="video-container" id="video-container-${page}">
                        <video id="video-${page}" muted loop playsinline style="display: none;">
                            <source src="" type="video/mp4">
                        </video>
                        <div class="loading" id="loading-${page}">等待测试</div>
                    </div>
                    <div class="info">
                        <div><strong>状态</strong>: <span class="status-badge status-waiting" id="status-${page}">等待</span></div>
                        <div><strong>URL</strong>: <span id="url-${page}">-</span></div>
                        <div><strong>加载时间</strong>: <span id="time-${page}">-</span></div>
                        <div><strong>文件大小</strong>: <span id="size-${page}">-</span></div>
                        <div><strong>传输速度</strong>: <span id="speed-${page}">-</span></div>
                        <div><strong>错误信息</strong>: <span id="error-${page}">-</span></div>
                    </div>
                `;
                
                videoGrid.appendChild(videoItem);
            });
        }

        // 开始串行测试
        async function startSerialTest() {
            if (testState.isRunning) {
                log('⚠️ 测试正在进行中，请等待完成', 'warning');
                return;
            }

            log('🚀 开始VPS本地服务串行测试');
            testState.isRunning = true;
            testState.currentIndex = 0;
            testState.results = {};
            testState.startTime = Date.now();

            updateGlobalStatus('🔄 正在进行串行测试...');

            try {
                // 设置VPS_ONLY策略
                testState.videoLoader.setLoadingOrder(['tertiary']);
                log(`📋 设置加载策略: VPS_ONLY (仅第三层)`);

                for (let i = 0; i < TEST_CONFIG.pages.length; i++) {
                    const page = TEST_CONFIG.pages[i];
                    testState.currentIndex = i;
                    
                    log(`\n🎬 测试视频 ${i + 1}/${TEST_CONFIG.pages.length}: ${TEST_CONFIG.pageNames[page]}`);
                    
                    try {
                        await testSingleVideo(page);
                        
                        if (i < TEST_CONFIG.pages.length - 1) {
                            log(`⏳ 等待 ${TEST_CONFIG.serialDelay}ms 后继续下一个测试`);
                            await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.serialDelay));
                        }
                    } catch (error) {
                        log(`❌ 视频 ${page} 测试失败: ${error.message}`, 'error');
                        testState.results[page] = { success: false, error: error.message };
                    }
                }

                // 显示最终结果
                showFinalResults();

            } catch (error) {
                log(`❌ 串行测试失败: ${error.message}`, 'error');
                updateGlobalStatus('❌ 测试失败');
            } finally {
                testState.isRunning = false;
            }
        }

        // 测试单个视频
        async function testSingleVideo(page) {
            const videoElement = document.getElementById(`video-${page}`);
            const statusElement = document.getElementById(`status-${page}`);
            const urlElement = document.getElementById(`url-${page}`);
            const timeElement = document.getElementById(`time-${page}`);
            const sizeElement = document.getElementById(`size-${page}`);
            const speedElement = document.getElementById(`speed-${page}`);
            const errorElement = document.getElementById(`error-${page}`);
            const loadingElement = document.getElementById(`loading-${page}`);

            // 重置状态
            statusElement.className = 'status-badge status-loading';
            statusElement.textContent = '加载中';
            loadingElement.textContent = '正在加载...';
            videoElement.style.display = 'none';
            errorElement.textContent = '-';

            const startTime = Date.now();

            try {
                // 生成VPS URL
                const vpsUrl = `/video/compressed/${page}.mp4`;
                urlElement.textContent = vpsUrl;
                log(`🔗 VPS URL: ${vpsUrl}`);

                // 使用VideoLoader加载视频
                await testState.videoLoader.loadVideo(page, videoElement);

                const loadTime = Date.now() - startTime;

                // 获取视频文件信息
                const fileSize = await getVideoFileSize(vpsUrl);
                const transferSpeed = fileSize > 0 ? (fileSize / (loadTime / 1000) / 1024 / 1024).toFixed(2) : 'N/A';

                // 更新显示
                statusElement.className = 'status-badge status-success';
                statusElement.textContent = '成功';
                timeElement.textContent = `${loadTime}ms`;
                sizeElement.textContent = fileSize > 0 ? `${(fileSize / 1024 / 1024).toFixed(2)}MB` : 'N/A';
                speedElement.textContent = transferSpeed !== 'N/A' ? `${transferSpeed}MB/s` : 'N/A';

                loadingElement.style.display = 'none';
                videoElement.style.display = 'block';

                testState.results[page] = {
                    success: true,
                    loadTime: loadTime,
                    fileSize: fileSize,
                    transferSpeed: transferSpeed,
                    url: vpsUrl
                };

                log(`✅ ${TEST_CONFIG.pageNames[page]} 加载成功 (${loadTime}ms, ${transferSpeed}MB/s)`, 'success');

            } catch (error) {
                statusElement.className = 'status-badge status-error';
                statusElement.textContent = '失败';
                errorElement.textContent = error.message;
                loadingElement.textContent = '加载失败';

                testState.results[page] = {
                    success: false,
                    error: error.message,
                    url: `/video/compressed/${page}.mp4`
                };

                log(`❌ ${TEST_CONFIG.pageNames[page]} 加载失败: ${error.message}`, 'error');
                throw error;
            }
        }

        // 获取视频文件大小
        async function getVideoFileSize(url) {
            try {
                const response = await fetch(url, { method: 'HEAD' });
                const contentLength = response.headers.get('content-length');
                return contentLength ? parseInt(contentLength) : 0;
            } catch (error) {
                log(`⚠️ 无法获取文件大小: ${error.message}`, 'warning');
                return 0;
            }
        }

        // 测试第一个视频
        async function testFirstVideo() {
            if (testState.isRunning) {
                log('⚠️ 测试正在进行中，请等待完成', 'warning');
                return;
            }

            const firstPage = TEST_CONFIG.pages[0];
            log(`🎯 开始测试第一个视频: ${TEST_CONFIG.pageNames[firstPage]}`);

            testState.isRunning = true;
            updateGlobalStatus('🔄 正在测试第一个视频...');

            try {
                // 设置VPS_ONLY策略
                testState.videoLoader.setLoadingOrder(['tertiary']);
                await testSingleVideo(firstPage);
                updateGlobalStatus('✅ 第一个视频测试完成');
            } catch (error) {
                updateGlobalStatus('❌ 第一个视频测试失败');
            } finally {
                testState.isRunning = false;
            }
        }

        // 重置测试
        function resetTest() {
            log('🔄 重置测试状态');

            testState.isRunning = false;
            testState.currentIndex = 0;
            testState.results = {};
            testState.startTime = null;

            // 重置所有视频项状态
            TEST_CONFIG.pages.forEach(page => {
                const statusElement = document.getElementById(`status-${page}`);
                const urlElement = document.getElementById(`url-${page}`);
                const timeElement = document.getElementById(`time-${page}`);
                const sizeElement = document.getElementById(`size-${page}`);
                const speedElement = document.getElementById(`speed-${page}`);
                const errorElement = document.getElementById(`error-${page}`);
                const loadingElement = document.getElementById(`loading-${page}`);
                const videoElement = document.getElementById(`video-${page}`);

                statusElement.className = 'status-badge status-waiting';
                statusElement.textContent = '等待';
                urlElement.textContent = '-';
                timeElement.textContent = '-';
                sizeElement.textContent = '-';
                speedElement.textContent = '-';
                errorElement.textContent = '-';
                loadingElement.textContent = '等待测试';
                loadingElement.style.display = 'block';
                videoElement.style.display = 'none';
                videoElement.src = '';
            });

            // 隐藏性能统计
            document.getElementById('performanceStats').style.display = 'none';

            updateGlobalStatus('等待开始测试');

            // 清空日志
            document.getElementById('logContainer').innerHTML = '<strong>📋 测试日志</strong><br>测试已重置，等待开始...';

            log('✅ 测试状态已重置');
        }

        // 更新全局状态
        function updateGlobalStatus(status) {
            document.getElementById('globalStatus').innerHTML = `<strong>状态</strong>: ${status}`;
        }

        // 显示最终结果
        function showFinalResults() {
            const totalTime = Date.now() - testState.startTime;
            const successCount = Object.values(testState.results).filter(r => r.success).length;
            const totalCount = TEST_CONFIG.pages.length;

            log(`\n📊 测试完成统计:`);
            log(`   总耗时: ${totalTime}ms`);
            log(`   成功率: ${successCount}/${totalCount} (${(successCount/totalCount*100).toFixed(1)}%)`);

            if (successCount === totalCount) {
                updateGlobalStatus(`✅ 所有测试通过 (${successCount}/${totalCount})`);
                log('🎉 VPS本地服务测试完美通过！', 'success');
            } else {
                updateGlobalStatus(`⚠️ 部分测试失败 (${successCount}/${totalCount})`);
                log('⚠️ 部分VPS测试失败，需要检查配置', 'warning');
            }

            // 显示性能统计
            showPerformanceStats();
        }

        // 显示性能统计
        function showPerformanceStats() {
            const statsContainer = document.getElementById('performanceStats');
            const statsContent = document.getElementById('statsContent');

            let totalSize = 0;
            let totalTime = 0;
            let successCount = 0;

            let statsHtml = '';

            TEST_CONFIG.pages.forEach(page => {
                const result = testState.results[page];
                if (result && result.success) {
                    totalSize += result.fileSize || 0;
                    totalTime += result.loadTime || 0;
                    successCount++;

                    statsHtml += `
                        <div class="stat-item">
                            <strong>${TEST_CONFIG.pageNames[page]}</strong><br>
                            ${result.loadTime}ms | ${(result.fileSize/1024/1024).toFixed(2)}MB | ${result.transferSpeed}MB/s
                        </div>
                    `;
                }
            });

            if (successCount > 0) {
                const avgTime = (totalTime / successCount).toFixed(0);
                const avgSize = (totalSize / successCount / 1024 / 1024).toFixed(2);
                const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);

                statsHtml = `
                    <div class="stat-item">
                        <strong>📈 总体统计</strong><br>
                        成功: ${successCount}/${TEST_CONFIG.pages.length} |
                        总大小: ${totalSizeMB}MB |
                        平均时间: ${avgTime}ms |
                        平均大小: ${avgSize}MB
                    </div>
                ` + statsHtml;

                statsContent.innerHTML = statsHtml;
                statsContainer.style.display = 'block';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
