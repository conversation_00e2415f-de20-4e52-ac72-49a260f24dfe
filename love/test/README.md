# Love Website 测试中心

## 📋 测试列表

### � R2视频加载测试
**文件**: `r2-video-loading-test.html`
**访问**: https://love.yuh.cool/test/r2-video-loading-test.html

**功能**:
- 测试R2存储桶视频加载功能
- 串行加载5个视频，解决并发冲突问题
- 独立VideoLoader实例，避免状态冲突

**操作**:
- 🚀 开始串行测试：测试所有视频
- 🔄 重置测试：重置状态
- 🎯 测试第一个：单独测试调试

**状态**: ✅ 已完成 - 所有视频正常加载

### 🌩️ Cloudinary视频加载测试
**文件**: `cloudinary-video-loading-test.html`
**访问**: https://love.yuh.cool/test/cloudinary-video-loading-test.html

**功能**:
- 测试Cloudinary多账户的视频加载功能
- 使用CLOUDINARY_ONLY模式验证第二层CDN
- 验证5个账户(YU0-YU4)的映射正确性
- 串行测试避免并发冲突，独立VideoLoader实例
- 实时性能监控和详细错误报告

**操作**:
- 🚀 开始串行测试：测试所有5个页面视频
- 🎯 测试第一个：单独测试首页视频调试
- 🔄 重置测试：重置状态和结果

**账户映射**:
- 🏠 Home → YU0 (dcglebc2w)
- 💕 Anniversary → YU1 (drhqbbqxz)
- 👫 Meetings → YU2 (dkqnm9nwr)
- 🌟 Memorial → YU3 (ds14sv2gh)
- 💑 Together Days → YU4 (dpq95x5nf)

**技术特性**:
- 参考R2测试页面的设计风格和测试方式
- 独立VideoLoader实例避免状态冲突
- 串行测试机制，每个视频间隔1秒
- 完整的错误处理和状态反馈

**状态**: 🔄 待测试 - 验证第二层CDN性能

### 🖥️ VPS视频加载测试
**文件**: `vps-video-loading-test.html`
**访问**: https://love.yuh.cool/test/vps-video-loading-test.html

**功能**:
- 测试VPS本地视频服务功能，使用VPS_ONLY模式验证第三层保障
- 验证Nginx配置和本地文件服务性能
- 测试CORS配置和跨域访问
- 串行测试避免并发冲突，独立VideoLoader实例
- 实时性能监控和详细错误报告

**操作**:
- 🚀 开始串行测试：测试所有5个页面视频
- 🎯 测试第一个：单独测试首页视频调试
- 🔄 重置测试：重置状态和结果

**技术特性**:
- VPS_ONLY策略：仅使用第三层本地服务器
- 本地路径：`/video/compressed/` 目录服务
- 性能监控：加载时间、文件大小、传输速度
- 错误处理：详细的错误信息和状态反馈
- 缓存验证：测试Nginx缓存策略效果

**验证目标**:
- ✅ 本地视频文件服务正常
- ✅ Nginx配置和缓存策略生效
- ✅ CORS配置正确
- ✅ 文件完整性和可用性
- ✅ 性能达标（10秒超时内）

**状态**: ✅ 已完成 - 验证第三层VPS保障性能

### ✨ 星空背景降级机制测试
**文件**: `starry-background-fallback-test.html`
**访问**: https://love.yuh.cool/test/starry-background-fallback-test.html

**功能**:
- 测试星空背景降级机制，模拟所有CDN层失效的极端情况
- 验证第四层保障的可靠性和用户体验
- 测试各页面的星空主题背景效果
- 验证动画效果和视觉体验
- 测试恢复机制和重试逻辑

**操作**:
- ✨ 直接测试星空背景：单独测试第四层保障机制
- 💥 模拟全层失效：模拟R2、Cloudinary、VPS全部失效
- 🔄 测试恢复机制：测试网络恢复后的重新加载
- 🔄 重置测试：重置测试环境

**页面主题**:
- 🏠 首页 → 粉色浪漫星空
- 💕 纪念日 → 金色温暖星空
- 👫 相遇记录 → 深紫神秘星空
- 🌟 纪念页面 → 蓝色海洋星空
- 💑 在一起的日子 → 橙色夕阳星空

**技术特性**:
- 独立的星空背景测试器
- 实时性能监控和统计
- 完整的错误处理和状态反馈
- 支持手动降级方案
- CSS动画效果验证

**验证目标**:
- ✅ 星空背景正常启用
- ✅ 各页面主题色彩正确
- ✅ 动画效果流畅
- ✅ 恢复机制有效
- ✅ 极端场景处理正常

**状态**: 🔄 待测试 - 验证第四层星空背景保障机制

---

## 🔧 测试开发指南

### 添加新测试
1. 在 `/test` 目录创建HTML文件
2. 使用描述性文件名，如 `功能-测试类型-test.html`
3. 在此README中添加测试说明

### 测试规范
- 包含详细的控制台日志
- 提供清晰的操作按钮
- 显示实时状态和结果
- 支持重置和重新测试

### 访问方式
所有测试页面通过以下方式访问：
```
https://love.yuh.cool/test/[文件名].html
```
