<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星空背景降级机制测试 - Love Website</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin: 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .test-section h2 {
            color: #4ecdc4;
            margin-top: 0;
            font-size: 1.5em;
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }

        .test-button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .test-button.primary {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .test-button.secondary {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
        }

        .test-button.tertiary {
            background: linear-gradient(45deg, #45b7d1, #2980b9);
            color: white;
        }

        .test-button.quaternary {
            background: linear-gradient(45deg, #96ceb4, #2ecc71);
            color: white;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .test-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .video-container {
            position: relative;
            width: 100%;
            height: 300px;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .video-background {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #000;
        }

        .video-background video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .test-status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            max-height: 300px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .log-entry {
            margin: 5px 0;
            padding: 5px 10px;
            border-radius: 4px;
        }

        .log-entry.success {
            background: rgba(46, 204, 113, 0.2);
            border-left: 3px solid #2ecc71;
        }

        .log-entry.error {
            background: rgba(231, 76, 60, 0.2);
            border-left: 3px solid #e74c3c;
        }

        .log-entry.info {
            background: rgba(52, 152, 219, 0.2);
            border-left: 3px solid #3498db;
        }

        .log-entry.warning {
            background: rgba(241, 196, 15, 0.2);
            border-left: 3px solid #f1c40f;
        }

        .page-selector {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .page-button {
            padding: 8px 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .page-button.active {
            background: rgba(255, 255, 255, 0.3);
            border-color: #4ecdc4;
        }

        .page-button:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #4ecdc4;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>✨ 星空背景降级机制测试</h1>
            <p>测试四层CDN架构的第四层保障机制 - 模拟极端故障场景</p>
        </div>

        <!-- 页面选择器 -->
        <div class="test-section">
            <h2>🎯 页面选择</h2>
            <div class="page-selector">
                <div class="page-button active" data-page="home">🏠 首页</div>
                <div class="page-button" data-page="anniversary">💕 纪念日</div>
                <div class="page-button" data-page="meetings">👫 相遇记录</div>
                <div class="page-button" data-page="memorial">🌟 纪念页面</div>
                <div class="page-button" data-page="together-days">💑 在一起的日子</div>
            </div>
        </div>

        <!-- 测试控制 -->
        <div class="test-section">
            <h2>🧪 测试控制</h2>
            <div class="button-group">
                <button class="test-button quaternary" onclick="testStarryBackgroundOnly()">
                    ✨ 直接测试星空背景
                </button>
                <button class="test-button primary" onclick="testAllLayersFailure()">
                    💥 模拟全层失效
                </button>
                <button class="test-button secondary" onclick="testRecoveryMechanism()">
                    🔄 测试恢复机制
                </button>
                <button class="test-button tertiary" onclick="resetTest()">
                    🔄 重置测试
                </button>
            </div>
        </div>

        <!-- 视频容器 -->
        <div class="test-section">
            <h2>📺 视频测试区域</h2>
            <div class="video-container">
                <div class="video-background" id="video-container">
                    <video id="test-video" autoplay muted loop playsinline style="display: none;">
                        <source src="" type="video/mp4">
                        您的浏览器不支持视频播放
                    </video>
                    <div id="fallback-message" style="color: rgba(255,255,255,0.7); text-align: center;">
                        点击测试按钮开始测试
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试状态 -->
        <div class="test-section">
            <h2>📊 测试状态</h2>
            <div class="test-status" id="test-log">
                <div class="log-entry info">等待测试开始...</div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="test-section">
            <h2>📈 测试统计</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="total-tests">0</div>
                    <div class="stat-label">总测试次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="starry-activations">0</div>
                    <div class="stat-label">星空背景启用次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="recovery-tests">0</div>
                    <div class="stat-label">恢复机制测试次数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="avg-load-time">0ms</div>
                    <div class="stat-label">平均加载时间</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入视频加载器 -->
    <script src="/src/client/scripts/video-loader.js"></script>

    <script>
        // 测试状态管理
        class StarryBackgroundTester {
            constructor() {
                this.currentPage = 'home';
                this.testStats = {
                    totalTests: 0,
                    starryActivations: 0,
                    recoveryTests: 0,
                    loadTimes: []
                };
                this.videoLoader = null;
                this.testStartTime = 0;

                this.init();
            }

            async init() {
                this.log('🚀 初始化星空背景测试器...', 'info');

                // 等待视频加载器初始化
                if (window.videoLoader) {
                    this.videoLoader = window.videoLoader;
                    await this.videoLoader.init();
                    this.log('✅ 视频加载器初始化完成', 'success');
                } else {
                    this.log('⚠️ 视频加载器未找到，创建独立实例', 'warning');
                    // 这里可以创建独立的加载器实例
                }

                this.setupEventListeners();
                this.log('🎯 测试器准备就绪，当前页面: ' + this.currentPage, 'info');
            }

            setupEventListeners() {
                // 页面选择器事件
                document.querySelectorAll('.page-button').forEach(button => {
                    button.addEventListener('click', (e) => {
                        document.querySelectorAll('.page-button').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentPage = e.target.dataset.page;
                        this.log(`📄 切换到页面: ${this.currentPage}`, 'info');
                    });
                });
            }

            log(message, type = 'info') {
                const logContainer = document.getElementById('test-log');
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${type}`;
                logEntry.innerHTML = `[${timestamp}] ${message}`;

                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;

                // 控制台同步输出
                console.log(`[StarryTest] ${message}`);
            }

            updateStats() {
                document.getElementById('total-tests').textContent = this.testStats.totalTests;
                document.getElementById('starry-activations').textContent = this.testStats.starryActivations;
                document.getElementById('recovery-tests').textContent = this.testStats.recoveryTests;

                const avgTime = this.testStats.loadTimes.length > 0
                    ? Math.round(this.testStats.loadTimes.reduce((a, b) => a + b, 0) / this.testStats.loadTimes.length)
                    : 0;
                document.getElementById('avg-load-time').textContent = avgTime + 'ms';
            }

            startTimer() {
                this.testStartTime = performance.now();
            }

            endTimer() {
                if (this.testStartTime > 0) {
                    const loadTime = performance.now() - this.testStartTime;
                    this.testStats.loadTimes.push(loadTime);
                    this.testStartTime = 0;
                    return loadTime;
                }
                return 0;
            }

            // 直接测试星空背景
            async testStarryBackgroundOnly() {
                this.log('✨ 开始直接测试星空背景...', 'info');
                this.testStats.totalTests++;
                this.startTimer();

                try {
                    const videoElement = document.getElementById('test-video');
                    const container = document.getElementById('video-container');

                    // 清理容器
                    this.clearVideoContainer();

                    // 直接调用星空背景加载
                    if (this.videoLoader && this.videoLoader.loadStarryBackground) {
                        const result = await this.videoLoader.loadStarryBackground(this.currentPage, videoElement, {
                            onSuccess: (layer) => {
                                this.log(`🌟 星空背景启用成功 (${layer})`, 'success');
                                this.testStats.starryActivations++;
                            }
                        });

                        const loadTime = this.endTimer();
                        this.log(`⏱️ 星空背景加载时间: ${Math.round(loadTime)}ms`, 'info');

                        if (result) {
                            this.log('✅ 星空背景测试通过', 'success');
                        } else {
                            this.log('❌ 星空背景测试失败', 'error');
                        }
                    } else {
                        // 降级方案：手动应用星空背景
                        this.log('⚠️ 使用降级方案手动应用星空背景', 'warning');
                        await this.manualStarryBackground();
                    }

                } catch (error) {
                    this.log(`❌ 星空背景测试异常: ${error.message}`, 'error');
                } finally {
                    this.updateStats();
                }
            }

            // 手动星空背景降级方案
            async manualStarryBackground() {
                const container = document.getElementById('video-container');
                const videoElement = document.getElementById('test-video');

                // 隐藏视频元素
                videoElement.style.display = 'none';

                // 加载星空背景CSS
                await this.ensureStarryCSS();

                // 应用星空背景类
                container.classList.add('starry-fallback', 'starry-background', this.currentPage);

                // 设置主题背景
                const themeBackgrounds = {
                    home: 'linear-gradient(135deg, #fce7f3 0%, #fbcfe8 25%, #f9a8d4 50%, #f472b6 75%, #ec4899 100%)',
                    anniversary: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 25%, #fbbf24 50%, #f59e0b 75%, #d97706 100%)',
                    meetings: 'linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 25%, #a5b4fc 50%, #818cf8 75%, #6366f1 100%)',
                    memorial: 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 25%, #93c5fd 50%, #60a5fa 75%, #3b82f6 100%)',
                    'together-days': 'linear-gradient(135deg, #fed7aa 0%, #fdba74 25%, #fb923c 50%, #f97316 75%, #ea580c 100%)'
                };

                container.style.background = themeBackgrounds[this.currentPage] || themeBackgrounds.home;

                // 添加加载指示器
                const indicator = document.createElement('div');
                indicator.className = 'starry-loading';
                indicator.innerHTML = `
                    <div style="font-size: 24px; margin-bottom: 10px;">✨</div>
                    <div>正在启用${this.getPageDisplayName()}星空背景...</div>
                `;
                container.appendChild(indicator);

                // 延迟移除指示器，模拟加载过程
                setTimeout(() => {
                    if (indicator.parentNode) {
                        indicator.remove();
                    }
                    container.classList.add('video-loaded');
                    this.testStats.starryActivations++;
                    this.log(`🎨 应用${this.getPageDisplayName()}星空主题背景`, 'success');
                }, 800);

                const loadTime = this.endTimer();
                this.log(`⏱️ 手动星空背景加载时间: ${Math.round(loadTime)}ms`, 'info');
            }

            async ensureStarryCSS() {
                return new Promise((resolve) => {
                    const existingLink = document.querySelector('link[href*="starry-background.css"]');
                    if (existingLink) {
                        resolve();
                        return;
                    }

                    const link = document.createElement('link');
                    link.rel = 'stylesheet';
                    link.href = '/src/client/styles/starry-background.css';

                    link.onload = () => {
                        this.log('✅ 星空背景CSS加载成功', 'success');
                        resolve();
                    };

                    link.onerror = () => {
                        this.log('⚠️ 星空背景CSS加载失败，使用内联样式', 'warning');
                        this.injectInlineStarryCSS();
                        resolve();
                    };

                    document.head.appendChild(link);

                    setTimeout(() => {
                        this.log('⚠️ 星空背景CSS加载超时', 'warning');
                        resolve();
                    }, 3000);
                });
            }

            injectInlineStarryCSS() {
                const style = document.createElement('style');
                style.id = 'starry-background-inline';
                style.textContent = `
                    .starry-background {
                        position: relative;
                        width: 100%;
                        height: 100%;
                        overflow: hidden;
                    }
                    .starry-background::before {
                        content: '';
                        position: absolute;
                        top: 0; left: 0;
                        width: 100%; height: 100%;
                        background-image:
                            radial-gradient(2px 2px at 20px 30px, #eee, transparent),
                            radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                            radial-gradient(1px 1px at 90px 40px, #fff, transparent);
                        background-repeat: repeat;
                        background-size: 200px 100px;
                        animation: starryMove 20s linear infinite;
                        opacity: 0.8;
                    }
                    @keyframes starryMove {
                        0% { transform: translateX(0) translateY(0); }
                        100% { transform: translateX(-200px) translateY(-100px); }
                    }
                `;
                document.head.appendChild(style);
            }

            getPageDisplayName() {
                const names = {
                    home: '首页',
                    anniversary: '纪念日',
                    meetings: '相遇记录',
                    memorial: '纪念页面',
                    'together-days': '在一起的日子'
                };
                return names[this.currentPage] || this.currentPage;
            }

            clearVideoContainer() {
                const container = document.getElementById('video-container');
                const videoElement = document.getElementById('test-video');

                // 清理所有星空背景相关的类
                container.className = 'video-background';
                container.style.background = '#000';

                // 清理所有子元素（除了video和fallback-message）
                const children = Array.from(container.children);
                children.forEach(child => {
                    if (child.id !== 'test-video' && child.id !== 'fallback-message') {
                        child.remove();
                    }
                });

                // 重置video元素
                videoElement.style.display = 'none';
                videoElement.src = '';

                // 显示默认消息
                const fallbackMessage = document.getElementById('fallback-message');
                if (fallbackMessage) {
                    fallbackMessage.style.display = 'block';
                    fallbackMessage.textContent = '测试进行中...';
                }
            }
        }

        // 全局测试实例
        let starryTester;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            starryTester = new StarryBackgroundTester();
        });

        // 全局测试函数
        async function testStarryBackgroundOnly() {
            if (starryTester) {
                await starryTester.testStarryBackgroundOnly();
            }
        }

        async function testAllLayersFailure() {
            if (!starryTester) return;

            starryTester.log('💥 开始模拟全层CDN失效测试...', 'info');
            starryTester.testStats.totalTests++;
            starryTester.startTimer();

            try {
                // 模拟使用STARRY_ONLY策略
                if (starryTester.videoLoader) {
                    // 临时设置为仅星空背景策略
                    const originalOrder = starryTester.videoLoader.loadingOrder;
                    starryTester.videoLoader.setLoadingOrder(['quaternary']);

                    starryTester.log('🔧 设置为STARRY_ONLY策略', 'info');

                    const videoElement = document.getElementById('test-video');
                    await starryTester.videoLoader.loadVideo(starryTester.currentPage, videoElement);

                    // 恢复原始策略
                    starryTester.videoLoader.setLoadingOrder(originalOrder);

                    const loadTime = starryTester.endTimer();
                    starryTester.log(`⏱️ 全层失效测试完成，耗时: ${Math.round(loadTime)}ms`, 'success');
                } else {
                    // 降级到手动测试
                    await starryTester.manualStarryBackground();
                }

            } catch (error) {
                starryTester.log(`❌ 全层失效测试异常: ${error.message}`, 'error');
            } finally {
                starryTester.updateStats();
            }
        }

        async function testRecoveryMechanism() {
            if (!starryTester) return;

            starryTester.log('🔄 开始测试恢复机制...', 'info');
            starryTester.testStats.recoveryTests++;

            // 先触发星空背景
            await testStarryBackgroundOnly();

            // 等待2秒后模拟网络恢复
            setTimeout(async () => {
                starryTester.log('🌐 模拟网络恢复，尝试重新加载视频...', 'info');

                if (starryTester.videoLoader) {
                    try {
                        const videoElement = document.getElementById('test-video');
                        // 重置加载器状态
                        starryTester.clearVideoContainer();

                        // 尝试正常加载流程
                        await starryTester.videoLoader.loadVideo(starryTester.currentPage, videoElement);
                        starryTester.log('✅ 恢复机制测试完成', 'success');
                    } catch (error) {
                        starryTester.log(`⚠️ 恢复测试失败，保持星空背景: ${error.message}`, 'warning');
                    }
                }

                starryTester.updateStats();
            }, 2000);
        }

        function resetTest() {
            if (!starryTester) return;

            starryTester.log('🔄 重置测试环境...', 'info');
            starryTester.clearVideoContainer();

            // 重置fallback消息
            const fallbackMessage = document.getElementById('fallback-message');
            if (fallbackMessage) {
                fallbackMessage.style.display = 'block';
                fallbackMessage.textContent = '点击测试按钮开始测试';
            }

            starryTester.log('✅ 测试环境已重置', 'success');
        }
    </script>
</body>
</html>
