# 四层视频架构部署指南 (v3.0)

## 📋 概述

本指南详细说明Love网站四层视频架构的完整部署流程，包括R2、Cloudinary、VPS和星空背景四层保障机制的配置和部署。

## 🏗️ 架构概览

### 四层架构设计
```
用户请求 → 智能加载器 → 四层降级策略
                        ↓
第一层: Cloudflare R2 (6秒超时) → 第二层: Cloudinary (7秒超时)
                                    ↓
第三层: VPS本地 (10秒超时) → 第四层: 星空背景 (无超时)
```

### 技术规格
- **视频编码**: H.265 (HEVC)
- **压缩策略**: CRF 14/16，无音频
- **文件大小**: 目标80MB以下
- **分辨率**: 保持原分辨率
- **兼容性**: yuv420p像素格式

## 🚀 部署前准备

### 1. 环境检查
```bash
# 检查必要工具
ffmpeg -version  # 需要支持libx265
node --version   # Node.js 16+
npm --version    # npm 8+

# 检查项目结构
cd /root/workspace/love
ls -la src/client/assets/videos/
ls -la src/client/assets/video-compressed/
```

### 2. 依赖安装
```bash
# 安装Node.js依赖
npm install cloudinary @aws-sdk/client-s3 dotenv

# 验证FFmpeg H.265支持
ffmpeg -encoders | grep libx265
```

### 3. 配置文件检查
```bash
# 检查环境变量配置
cat config/.env | grep VIDEO_DELIVERY_ENABLED
cat config/.env | grep VIDEO_LOADING_STRATEGY

# 验证配置完整性
node -e "console.log(require('./config/config.js').videoDelivery.enabled)"
```

## 🎬 视频处理部署

### 1. H.265视频压缩
```bash
# 执行智能压缩脚本
chmod +x scripts/compress-videos-for-cloudinary.sh
./scripts/compress-videos-for-cloudinary.sh

# 监控压缩进度
./scripts/monitor-compression.sh
```

### 2. 验证压缩结果
```bash
# 检查压缩后文件
ls -lh src/client/assets/video-compressed/

# 验证文件完整性
for file in src/client/assets/video-compressed/*.mp4; do
    echo "检查: $file"
    ffprobe "$file" 2>&1 | grep -E "(Duration|Video|Audio)"
done
```

## ☁️ 第一层: Cloudflare R2部署

### 1. R2配置验证
```bash
# 检查R2配置
grep -E "CLOUDFLARE_R2_" config/.env

# 验证R2连接
node -e "
const { S3Client } = require('@aws-sdk/client-s3');
const client = new S3Client({
    region: 'auto',
    endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
    credentials: {
        accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
        secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
    },
});
console.log('R2客户端创建成功');
"
```

### 2. R2视频上传
```bash
# 执行R2上传
node scripts/upload-r2.js

# 验证上传结果
curl -I https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/videos/home.mp4
```

## 🌤️ 第二层: Cloudinary部署

### 1. Cloudinary账户配置
```bash
# 检查Cloudinary密钥
grep -E "CLOUDINARY_SECRET_YU" config/.env

# 验证账户连接
node -e "
const cloudinary = require('cloudinary').v2;
cloudinary.config({
    cloud_name: 'dcglebc2w',
    api_key: '899226968246286',
    api_secret: process.env.CLOUDINARY_SECRET_YU0
});
console.log('Cloudinary配置成功');
"
```

### 2. Cloudinary视频上传
```bash
# 执行Cloudinary上传
node scripts/upload-cloudinary.js

# 验证上传结果
curl -I https://res.cloudinary.com/dcglebc2w/video/upload/love-website/home.mp4
```

## 🖥️ 第三层: VPS本地部署

### 1. VPS配置检查
```bash
# 检查VPS配置
grep -E "VPS_" config/.env

# 验证本地文件服务
ls -la src/client/assets/video-compressed/
```

### 2. Nginx配置更新
```bash
# 检查Nginx配置中的视频服务
grep -A 10 "location /video/compressed/" config/nginx-love-api.conf

# 重载Nginx配置
sudo nginx -t && sudo nginx -s reload
```

### 3. VPS服务验证
```bash
# 测试本地视频服务
curl -I https://love.yuh.cool/video/compressed/home.mp4
```

## ✨ 第四层: 星空背景部署

### 1. 星空背景样式检查
```bash
# 检查星空背景CSS
ls -la src/client/styles/starry-background.css

# 验证CSS内容
head -20 src/client/styles/starry-background.css
```

### 2. 星空背景配置
```bash
# 检查星空背景配置
grep -E "STARRY_BACKGROUND_" config/.env

# 验证星空背景服务
curl https://love.yuh.cool/src/client/styles/starry-background.css
```

## 🔧 前端集成部署

### 1. 智能加载器部署
```bash
# 检查智能加载器
ls -la src/client/scripts/video-loader.js

# 验证加载器集成
grep -l "video-loader.js" src/client/pages/*.html
```

### 2. 页面集成验证
```bash
# 检查页面集成
for page in src/client/pages/*.html; do
    echo "检查页面: $page"
    grep -E "(video-loader|background-video)" "$page"
done
```

## 🧪 部署验证测试

### 1. 分层测试
```bash
# R2层测试
curl -I https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/videos/home.mp4

# Cloudinary层测试
curl -I https://res.cloudinary.com/dcglebc2w/video/upload/love-website/home.mp4

# VPS层测试
curl -I https://love.yuh.cool/video/compressed/home.mp4

# 星空背景层测试
curl https://love.yuh.cool/src/client/styles/starry-background.css
```

### 2. 完整流程测试
```bash
# 访问测试页面
curl https://love.yuh.cool/test/r2-video-loading-test.html
curl https://love.yuh.cool/test/cloudinary-multi-account-test.html
curl https://love.yuh.cool/test/vps-video-loading-test.html
curl https://love.yuh.cool/test/starry-background-fallback-test.html
```

## 📊 部署状态监控

### 1. 配置状态检查
```bash
# 检查四层架构配置
curl https://love.yuh.cool/api/config | jq '.data.videoDelivery'

# 检查宏策略配置
curl https://love.yuh.cool/api/config | jq '.data.videoDelivery.macroConfig'
```

### 2. 服务状态监控
```bash
# 检查后端服务
./manage-love.sh status

# 检查视频文件状态
ls -lh src/client/assets/video-compressed/

# 检查压缩脚本状态
ps aux | grep compress-videos
```

## 🔧 故障排除

### 常见问题解决

#### 1. R2上传失败
```bash
# 检查R2配置
echo $CLOUDFLARE_R2_ACCESS_KEY_ID
echo $CLOUDFLARE_R2_BUCKET

# 重新上传
node scripts/upload-r2.js
```

#### 2. Cloudinary上传失败
```bash
# 检查Cloudinary密钥
echo $CLOUDINARY_SECRET_YU0

# 清理并重新上传
node scripts/upload-cloudinary.js
```

#### 3. 视频加载失败
```bash
# 检查智能加载器
curl https://love.yuh.cool/src/client/scripts/video-loader.js

# 检查配置API
curl https://love.yuh.cool/api/config
```

## 📈 性能优化建议

### 1. 压缩优化
- 使用CRF 14获得最佳画质
- 大文件(>90MB)使用CRF 16平衡大小和画质
- 移除音频轨道减少文件大小

### 2. 缓存优化
- R2设置长期缓存头
- Cloudinary启用自动优化
- VPS配置Nginx缓存

### 3. 监控优化
- 定期检查各层可用性
- 监控视频加载时间
- 分析用户访问模式

---

**部署完成标志**: 所有四层都能正常响应，智能加载器工作正常，测试页面全部通过。
