# 四层视频架构运维手册 (v3.0)

## 📋 概述

本手册提供Love网站四层视频架构的日常运维、监控、故障处理和维护指南。

## 🎯 运维目标

- **可用性**: 99.9%视频加载成功率
- **性能**: 平均加载时间 < 3秒
- **稳定性**: 四层降级机制正常工作
- **安全性**: 所有层级访问安全可控

## 📊 日常监控

### 1. 四层架构状态监控
```bash
# 每日检查脚本
#!/bin/bash
echo "=== 四层视频架构状态检查 ==="
echo "时间: $(date)"

# 检查配置状态
echo "1. 配置状态检查:"
curl -s https://love.yuh.cool/api/config | jq '.data.videoDelivery.enabled'

# 检查R2层
echo "2. R2层状态检查:"
curl -I -s https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/videos/home.mp4 | head -1

# 检查Cloudinary层
echo "3. Cloudinary层状态检查:"
curl -I -s https://res.cloudinary.com/dcglebc2w/video/upload/love-website/home.mp4 | head -1

# 检查VPS层
echo "4. VPS层状态检查:"
curl -I -s https://love.yuh.cool/video/compressed/home.mp4 | head -1

# 检查星空背景层
echo "5. 星空背景层状态检查:"
curl -s https://love.yuh.cool/src/client/styles/starry-background.css | head -5

echo "=== 检查完成 ==="
```

### 2. 性能监控指标
```bash
# 性能监控脚本
#!/bin/bash
echo "=== 性能监控报告 ==="

# 检查视频文件大小
echo "视频文件大小统计:"
ls -lh src/client/assets/video-compressed/ | awk '{print $5, $9}'

# 检查服务响应时间
echo "服务响应时间测试:"
time curl -s https://love.yuh.cool/api/health > /dev/null

# 检查磁盘使用
echo "磁盘使用情况:"
df -h | grep -E "(/$|/root)"

# 检查内存使用
echo "内存使用情况:"
free -h
```

### 3. 自动化监控脚本
```bash
# 创建监控cron任务
# 编辑 crontab -e 添加:
# 0 */6 * * * /root/workspace/love/scripts/monitor-four-layer.sh >> /var/log/love-monitor.log 2>&1
# 0 0 * * * /root/workspace/love/scripts/daily-health-check.sh >> /var/log/love-health.log 2>&1
```

## 🔧 维护任务

### 1. 每日维护
```bash
# 每日维护检查清单
echo "=== 每日维护任务 ==="

# 1. 检查服务状态
./manage-love.sh status

# 2. 检查日志错误
tail -50 logs/backend.log | grep -i error

# 3. 检查磁盘空间
df -h | awk '$5 > 80 {print "警告: " $1 " 磁盘使用率: " $5}'

# 4. 检查视频文件完整性
for file in src/client/assets/video-compressed/*.mp4; do
    if [ ! -f "$file" ]; then
        echo "警告: 视频文件缺失 $file"
    fi
done

# 5. 检查四层架构配置
if [ "$(curl -s https://love.yuh.cool/api/config | jq -r '.data.videoDelivery.enabled')" != "true" ]; then
    echo "警告: 四层视频架构未启用"
fi
```

### 2. 每周维护
```bash
# 每周维护任务
echo "=== 每周维护任务 ==="

# 1. 数据库备份
./manage-love.sh
# 选择: 数据库管理 → 备份数据库

# 2. 清理旧日志
find logs/ -name "*.log" -mtime +7 -delete

# 3. 检查SSL证书
openssl x509 -in /etc/letsencrypt/live/love.yuh.cool/cert.pem -noout -dates

# 4. 更新系统包
sudo apt update && sudo apt list --upgradable

# 5. 检查四层架构性能
./scripts/performance-test.sh
```

### 3. 每月维护
```bash
# 每月维护任务
echo "=== 每月维护任务 ==="

# 1. 完整系统备份
tar -czf /backup/love-$(date +%Y%m%d).tar.gz /root/workspace/love/

# 2. 检查Cloudinary配额使用
node -e "
const cloudinary = require('cloudinary').v2;
// 检查各账户配额使用情况
"

# 3. 检查R2存储使用
# 登录Cloudflare控制台检查R2存储使用情况

# 4. 性能优化分析
# 分析访问日志，优化热点文件

# 5. 安全检查
# 检查访问日志中的异常请求
```

## 🚨 故障处理

### 1. R2层故障处理
```bash
# R2层故障诊断
echo "=== R2层故障诊断 ==="

# 检查R2连接
curl -I https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/videos/home.mp4

# 如果失败，检查配置
grep -E "CLOUDFLARE_R2_" config/.env

# 重新上传视频
node scripts/upload-r2.js

# 验证修复
curl -I https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/videos/home.mp4
```

### 2. Cloudinary层故障处理
```bash
# Cloudinary层故障诊断
echo "=== Cloudinary层故障诊断 ==="

# 检查各账户状态
for account in dcglebc2w drhqbbqxz dkqnm9nwr ds14sv2gh dpq95x5nf; do
    echo "检查账户: $account"
    curl -I https://res.cloudinary.com/$account/video/upload/love-website/home.mp4
done

# 如果失败，重新上传
node scripts/upload-cloudinary.js

# 检查配额使用
# 登录各Cloudinary账户检查使用情况
```

### 3. VPS层故障处理
```bash
# VPS层故障诊断
echo "=== VPS层故障诊断 ==="

# 检查本地文件
ls -la src/client/assets/video-compressed/

# 检查Nginx配置
nginx -t

# 检查文件权限
chmod 644 src/client/assets/video-compressed/*.mp4

# 重启Nginx
sudo systemctl restart nginx

# 验证修复
curl -I https://love.yuh.cool/video/compressed/home.mp4
```

### 4. 星空背景层故障处理
```bash
# 星空背景层故障诊断
echo "=== 星空背景层故障诊断 ==="

# 检查CSS文件
ls -la src/client/styles/starry-background.css

# 检查CSS内容
head -10 src/client/styles/starry-background.css

# 验证CSS服务
curl https://love.yuh.cool/src/client/styles/starry-background.css

# 如果需要，重新生成CSS
# 星空背景是CSS动画，通常不会失败
```

## 📈 性能优化

### 1. 视频压缩优化
```bash
# 重新压缩视频以优化性能
./scripts/compress-videos-for-cloudinary.sh

# 监控压缩进度
./scripts/monitor-compression.sh

# 验证压缩效果
ls -lh src/client/assets/video-compressed/
```

### 2. 缓存优化
```bash
# 检查Nginx缓存配置
grep -A 5 "expires" config/nginx-love-api.conf

# 清理浏览器缓存测试
curl -H "Cache-Control: no-cache" https://love.yuh.cool/video/compressed/home.mp4
```

### 3. 负载优化
```bash
# 检查服务器负载
top -n 1 | head -5

# 检查网络连接
ss -tuln | grep 1314

# 优化建议
echo "优化建议:"
echo "1. 定期清理日志文件"
echo "2. 监控磁盘使用率"
echo "3. 优化视频文件大小"
echo "4. 使用CDN加速"
```

## 🔐 安全维护

### 1. 访问控制
```bash
# 检查访问日志
tail -100 /var/log/nginx/access.log | grep -E "(video|mp4)"

# 检查异常访问
tail -100 /var/log/nginx/access.log | grep -E "(40[0-9]|50[0-9])"

# 检查SSL证书
openssl x509 -in /etc/letsencrypt/live/love.yuh.cool/cert.pem -noout -dates
```

### 2. 密钥管理
```bash
# 检查环境变量安全
ls -la config/.env
chmod 600 config/.env

# 验证密钥有效性
node -e "
require('dotenv').config({ path: './config/.env' });
console.log('R2密钥:', process.env.CLOUDFLARE_R2_ACCESS_KEY_ID ? '已配置' : '未配置');
console.log('Cloudinary密钥:', process.env.CLOUDINARY_SECRET_YU0 ? '已配置' : '未配置');
"
```

## 📊 监控报告

### 1. 生成日报
```bash
# 生成四层架构日报
./scripts/generate-daily-report.sh > reports/daily-$(date +%Y%m%d).txt
```

### 2. 生成周报
```bash
# 生成四层架构周报
./scripts/generate-weekly-report.sh > reports/weekly-$(date +%Y%m%d).txt
```

### 3. 生成月报
```bash
# 生成四层架构月报
./scripts/generate-monthly-report.sh > reports/monthly-$(date +%Y%m%d).txt
```

## 🆘 紧急响应

### 紧急情况处理流程
1. **立即评估**: 确定故障层级和影响范围
2. **快速恢复**: 使用应急策略恢复服务
3. **根因分析**: 分析故障原因
4. **永久修复**: 实施永久解决方案
5. **预防措施**: 更新监控和预防机制

### 应急联系方式
- **系统管理员**: [联系方式]
- **技术支持**: [联系方式]
- **服务商支持**: Cloudflare、Cloudinary技术支持

---

**运维成功标准**: 四层架构稳定运行，监控指标正常，故障快速恢复，用户体验良好。
