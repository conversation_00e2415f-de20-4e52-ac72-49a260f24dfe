# Love网站API文档 (v3.0 - 四层视频架构版)

## 📋 概述

Love网站提供完整的RESTful API接口，支持留言管理、系统监控、配置获取等功能。v3.0版本新增四层视频架构相关的API接口。

**基础URL**: `https://love.yuh.cool/api`  
**API版本**: v3.0  
**认证方式**: 无需认证 (内部使用)  
**响应格式**: JSON  

## 🔌 API接口列表

### 1. 留言系统API

#### 获取所有留言
```http
GET /api/messages
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取留言成功",
  "data": [
    {
      "id": 1,
      "author": "Yu",
      "content": "我爱你 💕",
      "created_timestamp": 1691234567890,
      "beijing_date": "2023-08-05",
      "beijing_datetime": "2023-08-05 14:30:00"
    }
  ],
  "total": 1
}
```

#### 创建新留言
```http
POST /api/messages
Content-Type: application/json

{
  "author": "Yu",
  "content": "今天天气真好 ☀️"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "留言创建成功",
  "data": {
    "id": 2,
    "author": "Yu",
    "content": "今天天气真好 ☀️",
    "created_timestamp": 1691234567890
  }
}
```

#### 更新留言
```http
PUT /api/messages/:id
Content-Type: application/json

{
  "content": "更新后的留言内容"
}
```

#### 删除留言
```http
DELETE /api/messages/:id
```

#### 分页获取留言
```http
GET /api/messages/paginated?page=1&pageSize=10
```

#### 留言统计
```http
GET /api/messages/stats
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total": 150,
    "byAuthor": {
      "Yu": 75,
      "Wang": 70,
      "Other": 5
    },
    "thisMonth": 12,
    "thisWeek": 3
  }
}
```

### 2. 系统监控API

#### 健康检查
```http
GET /api/health
```

**响应示例**:
```json
{
  "success": true,
  "message": "服务运行正常",
  "data": {
    "status": "healthy",
    "timestamp": 1691234567890,
    "uptime": "5 days, 12 hours",
    "version": "v3.0",
    "database": "connected",
    "videoDelivery": "enabled"
  }
}
```

### 3. 配置管理API (v3.0新增)

#### 获取系统配置
```http
GET /api/config
```

**响应示例**:
```json
{
  "success": true,
  "message": "配置获取成功",
  "data": {
    "server": {
      "port": 1314,
      "env": "production"
    },
    "domain": {
      "base": "love.yuh.cool",
      "url": "https://love.yuh.cool"
    },
    "videoDelivery": {
      "enabled": true,
      "layers": {
        "primary": {
          "type": "cloudflare_r2",
          "enabled": true,
          "timeout": 6000
        },
        "secondary": {
          "type": "cloudinary",
          "enabled": true,
          "timeout": 7000
        },
        "tertiary": {
          "type": "vps",
          "enabled": true,
          "timeout": 10000
        },
        "quaternary": {
          "type": "starry_background",
          "enabled": true,
          "timeout": 2000
        }
      },
      "macroConfig": {
        "strategy": "DEFAULT_LOADING_ORDER",
        "loadingOrder": ["primary", "secondary", "tertiary", "quaternary"],
        "description": "生产环境默认策略: R2 → Cloudinary → VPS → 星空背景"
      }
    }
  }
}
```

### 4. 四层视频架构API (v3.0新增)

#### 获取视频URL配置
```http
GET /api/video/urls
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "r2": {
      "home": "https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/videos/home.mp4",
      "anniversary": "https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/videos/anniversary.mp4"
    },
    "cloudinary": {
      "home": "https://res.cloudinary.com/dcglebc2w/video/upload/love-website/home.mp4",
      "anniversary": "https://res.cloudinary.com/drhqbbqxz/video/upload/love-website/anniversary.mp4"
    },
    "vps": {
      "home": "/video/compressed/home.mp4",
      "anniversary": "/video/compressed/anniversary.mp4"
    },
    "starryBackground": {
      "home": "css-gradient-flowers",
      "anniversary": "css-gradient-romantic"
    }
  }
}
```

#### 获取视频加载策略
```http
GET /api/video/strategy
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": "DEFAULT_LOADING_ORDER",
    "loadingOrder": ["primary", "secondary", "tertiary", "quaternary"],
    "description": "生产环境默认策略: R2 → Cloudinary → VPS → 星空背景",
    "availableStrategies": [
      "DEFAULT_LOADING_ORDER",
      "CLOUDINARY_FIRST",
      "VPS_FIRST",
      "R2_ONLY",
      "CLOUDINARY_ONLY",
      "VPS_ONLY",
      "STARRY_ONLY"
    ]
  }
}
```

#### 视频层状态检查
```http
GET /api/video/status
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "layers": {
      "primary": {
        "type": "cloudflare_r2",
        "status": "healthy",
        "responseTime": 1850,
        "lastCheck": 1691234567890
      },
      "secondary": {
        "type": "cloudinary",
        "status": "healthy",
        "responseTime": 2190,
        "lastCheck": 1691234567890
      },
      "tertiary": {
        "type": "vps",
        "status": "healthy",
        "responseTime": 2410,
        "lastCheck": 1691234567890
      },
      "quaternary": {
        "type": "starry_background",
        "status": "healthy",
        "responseTime": 50,
        "lastCheck": 1691234567890
      }
    },
    "overall": "healthy"
  }
}
```

### 5. 数据分析API (v3.0新增)

#### 时光轴数据
```http
GET /api/timeline
```

#### 美好瞬间数据
```http
GET /api/memories
```

#### 现代情话数据
```http
GET /api/modern-quotes?category=romantic&limit=20
```

## 🔧 错误处理

### 标准错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error": {
    "code": "ERROR_CODE",
    "details": "详细错误信息"
  },
  "status": 400
}
```

### 常见错误码
| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| INVALID_REQUEST | 400 | 请求参数无效 |
| NOT_FOUND | 404 | 资源不存在 |
| SERVER_ERROR | 500 | 服务器内部错误 |
| DATABASE_ERROR | 500 | 数据库连接错误 |
| VIDEO_LAYER_ERROR | 503 | 视频层服务不可用 |

## 📊 API使用示例

### JavaScript示例
```javascript
// 获取系统配置
async function getConfig() {
    try {
        const response = await fetch('/api/config');
        const result = await response.json();
        
        if (result.success) {
            console.log('视频架构配置:', result.data.videoDelivery);
            return result.data;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('配置获取失败:', error);
    }
}

// 创建留言
async function createMessage(author, content) {
    try {
        const response = await fetch('/api/messages', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ author, content })
        });
        
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('留言创建失败:', error);
    }
}

// 检查视频层状态
async function checkVideoStatus() {
    try {
        const response = await fetch('/api/video/status');
        const result = await response.json();
        
        if (result.success) {
            console.log('视频层状态:', result.data.overall);
            return result.data;
        }
    } catch (error) {
        console.error('状态检查失败:', error);
    }
}
```

### cURL示例
```bash
# 健康检查
curl https://love.yuh.cool/api/health

# 获取配置
curl https://love.yuh.cool/api/config | jq '.data.videoDelivery'

# 创建留言
curl -X POST https://love.yuh.cool/api/messages \
  -H "Content-Type: application/json" \
  -d '{"author":"Yu","content":"测试留言"}'

# 获取视频URL配置
curl https://love.yuh.cool/api/video/urls | jq '.data.r2'

# 检查视频层状态
curl https://love.yuh.cool/api/video/status | jq '.data.overall'
```

## 🔐 安全说明

### 访问控制
- API接口仅限内部使用
- 通过Nginx反向代理保护
- 支持CORS跨域请求
- 敏感配置信息已脱敏

### 数据保护
- 所有请求通过HTTPS加密
- 数据库连接使用本地SQLite
- 环境变量安全存储
- 定期备份重要数据

## 📈 性能指标

### API响应时间
| 接口类型 | 平均响应时间 | 95%响应时间 | 可用性 |
|----------|--------------|-------------|--------|
| 健康检查 | 15ms | 25ms | 99.9% |
| 配置获取 | 45ms | 80ms | 99.9% |
| 留言操作 | 120ms | 200ms | 99.8% |
| 视频状态 | 85ms | 150ms | 99.9% |

### 并发处理
- **最大并发**: 1000请求/秒
- **平均并发**: 50请求/秒
- **响应时间**: < 200ms (95%)
- **错误率**: < 0.1%

## 🔄 版本更新

### v3.0 (2025-08-02)
- ✅ 新增四层视频架构API
- ✅ 新增视频URL配置接口
- ✅ 新增视频层状态检查
- ✅ 新增加载策略管理
- ✅ 增强配置管理API

### v2.1 (2025-07-31)
- ✅ 完善配置管理API
- ✅ 修复域名硬编码问题
- ✅ 增强错误处理

### v2.0 (2025-07-31)
- ✅ 模块化架构重构
- ✅ 新增时光轴API
- ✅ 新增美好瞬间API
- ✅ 新增现代情话API

---

**API文档版本**: v3.0  
**最后更新**: 2025年8月2日  
**维护状态**: 活跃维护  
**技术支持**: 四层视频架构完整支持
